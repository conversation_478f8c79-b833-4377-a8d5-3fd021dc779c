"ui";
console.time("test");
ui.layout(
    <frame  bg="#eee8f4">
        <text id="welcomeText" text="欢迎使用无界！" gravity="center|bottom" marginBottom="15" textColor="#333333" textSize="20sp"/>
    </frame>
);
console.timeEnd("test");

    //更新对标库选择
    {
        var entries = "云对标库1（推荐）|云对标库2|本地对标库"
        var seed = storages.create("seednum");     
          var myAdapterListener = new android.widget.AdapterView.OnItemSelectedListener({
            onItemSelected: function (parent, view, position, id) {
                  seed.put("seed", id);
            }
          })
    }
    //轮播图设置
    {
      data = {
          // 图片宽高 一定要设置
          src_w: 750,
          src_h: 366,
          //time 图片切换时间
          time: 5000,
          data: [
              /*
              * {title,src,onClick}
              * title 图片标题 可省略
              * src 图片链接
              * onClick 图片点击触发函数事件  例:onClick="abc" 该图片被点击会触发imgOnClick()函数方法并传递abc字符串
              * 也可以统一触发到一个函数内 自行分辨每个触发事件
              */
              { onClick: "img_1", src: "https://vip.123pan.cn/1812381962/Api/%E6%97%A0%E7%95%8C/page1.png" },
              { onClick: "img_2", src: "https://vip.123pan.cn/1812381962/Api/%E6%97%A0%E7%95%8C/page2.png" },
              { onClick: "img_3", src: "https://vip.123pan.cn/1812381962/Api/%E6%97%A0%E7%95%8C/page3.png" }
          ],
      }
      
      tabs_view = [];
      tabs_index = 0;     
      //轮播图片点击触发事件
      var imgOnClick = function (text) {
          arr = data.data
          switch (text) {
              case arr[0].onClick:
                  var intent = new Intent();
                  intent.setAction(Intent.ACTION_VIEW);
                  intent.setData(android.net.Uri.parse("https://acnafehfcqw6.feishu.cn/wiki/B3tawoTqpiI8oukYCR9cuz6Rnrf"));
                  //intent.setPackage("com.android.browser"); 
                  // 替换为你想要使用的浏览器的包名
                  app.startActivity(intent);
                  break;
                  
              case arr[1].onClick:
                  toast("点击我要反馈，找我联系吧！")
                  break;
              case arr[2].onClick:
                  toast("点击我要反馈，找我联系吧！")
                  break;
          }
      }
      
      
      var imgLayout = function () {
          //继承ui.Widget
          util.extend(imgLayout, ui.Widget);
          function imgLayout() {
              //调用父类构造函数
              ui.Widget.call(this);
              //自定义属性data ,定义控件的每个参数 传入值为整数
              this.defineAttr("data", (view, attr, value, defineSetter) => {
                  //获取当前控件的参数值 data.data[value] 赋值到arr数组
                  let arr = data.data[value]
                  //设定 _text控件文本
                  arr.title = arr.title || false
                  arr.title ? view._text.setText(arr.title) : view._text.attr("visibility", "gone")
      
                  //设定 _src控件图片
                  view._src.attr("src", arr.src)
                  //设定 自定义点击事件
                  this._onClick = "imgOnClick('" + arr.onClick + "')"
              });
          }
          imgLayout.prototype.render = function () {
                  return (
                  <vertical>
                      <card w="*" h="auto" cardCornerRadius="20" margin="10 0" cardElevation="5" foreground="?selectableItemBackground">
                          <img id="_src" w="*" h="200" src="#ffffff" scaleType="fitXY" layout_gravity="center"/>
                          <text id="_text" w="*" h="20" text="" textColor="#99ffffff" bg="#80000000" textSize="14" layout_gravity="bottom" gravity="center"/>
                      </card>
                  </vertical>
                  )
          }
          imgLayout.prototype.onViewCreated = function (view) {
              view.click(() => {
                  eval(this._onClick);
              });
          };
          ui.registerWidget("img-layout", imgLayout);
          return imgLayout;
      }()
      
      var TabsLayout = function () {
          util.extend(TabsLayout, ui.Widget);
          function TabsLayout() {
              ui.Widget.call(this);
              this.defineAttr("data", (view, attr, value, defineSetter) => {
                  let arr = data.data
                  //遍历data.data数组添加对应数量的小白球
                  for (let i = 0; i < arr.length; i++) {
                      tabs_view[tabs_view.length] = ui.inflate(<text text="●" marginLeft="5" textSize="10sp"/>, view._tabs, true)
                  }
                  tabs_view[0].setTextColor(colors.parseColor("#ffffff"))
              });
          }
          TabsLayout.prototype.render = function () {
              return (
                  <horizontal id="_tabs" w="auto" h="auto" />
              )
          }
          ui.registerWidget("tabs-layout", TabsLayout);
          return TabsLayout;
      }()
      function view_hei() {
          _w = device.width / data.src_w
          return parseInt(data.src_h * _w)
      }
      
    }
    //自定义图标控件
    {
        /**
        * 获取Android状态栏高度（像素）
        * @returns {number} 状态栏高度，如果获取失败则返回0
        */
       function getStatusBarHeight() {
           try {
               // 获取系统资源
               var resources = context.getResources();
               
               // 获取状态栏高度的资源ID
               var resourceId = resources.getIdentifier("status_bar_height", "dimen", "android");
               
               // 获取状态栏高度的像素值
               var height = resources.getDimensionPixelSize(resourceId);
               
               return height;
           } catch (e) {
               // 如果出现异常（如无状态栏），返回0
               return 0;
           }
       }
       /**
        * 自定义图标控件（带背景和文本）
        */
       function IconWidget() {
           ui.Widget.call(this);
           
           // 定义控件属性
           this.defineAttr("src", function(view, attr, value, defineSetter) {
               view._src.attr("src", value);
           });
           
           this.defineAttr("text", function(view, attr, value, defineSetter) {
               view._text.setText(value);
           });
           
           this.defineAttr("bg", function(view, attr, value, defineSetter) {
               view._bg.attr("src", value);
           });
           
           this.defineAttr("tip", function(view, attr, value, defineSetter) {
               // 提示属性，暂无实现
           });
           
           this.defineAttr("tip2", function(view, attr, value, defineSetter) {
               // 提示属性2，暂无实现
           });
           
           // 布局加载完成回调
           this.onFinishInflation = function(view) {
               // 可在此处添加初始化代码
           };
           
           // 渲染控件布局
           this.render = function() {
               return `
                   <vertical>
                       <card alpha="1" margin="0" id="_cardView" cardCornerRadius="15" 
                           cardElevation="0" cardUseCompatPadding="true" cardBackgroundColor="#ffffff">
                           <horizontal layout_gravity="center" gravity="center">
                               <frame margin="2" layout_gravity="center" w="auto" h="auto" padding="2" alpha="1">
                                   <img id="_bg" w="42" h="42" radius="12" scaleType="center" />
                                   <img id="_src" w="25" h="25" src="@drawable/ic_mic_none_black_48dp" 
                                       tint="#ffffff" layout_gravity="center" />
                               </frame>
                           </horizontal>
                       </card>
                       <vertical margin="-10 -2" layout_gravity="right|center" gravity="center">
                           <text id="_text" gravity="center" textSize="12" textColor="#000000" text="内容" />
                       </vertical>
                   </vertical>
               `;
           };
       }
       
       function dip2px(dip) {
           return dip * context.getResources().getDisplayMetrics().density;
       }
    }
    {
        var statusBarHeight = getStatusBarHeight(); // 状态栏高度
        var bgWidth = parseInt(device.width);       // 背景宽度
        var bgHeight = parseInt(device.height*0.2);      // 背景高度
        var themeColor = "#373bd3";           // 主题色
        var deviceName = device.brand;        // 设备品牌
        var deviceModel = device.model;       // 设备型号
         //启动
    var pjysdk2 = require("./script/dyzf/PJYSDK.js");
    
    /* 将pjysdk.js文件中的代码复制粘贴到上面 */
    
    // AppKey 和 AppSecret 在泡椒云开发者后台获取
    var pjysdk = new pjysdk2("d1v9d6jdqusvqakj9a4g", "YDKnwvy14aIyZ7SJSbctUALz66tqJucS");
    // 监听心跳失败事件
    
    // 当脚本正常或者异常退出时会触发exit事件
    events.on("exit", function(){
    threads.start(function() {
    
        pjysdk.CardLogout(); // 调用退出登录
        log("结束运行");
        });
    });
    let login_ret;
    pjysdk.debug = true;
    var AuthorizationCode = (storages.create("AuthorizationCode").get("AuthorizationCode") == undefined || storages.create("AuthorizationCode").get("AuthorizationCode") == "") ? "未激活" : storages.create("AuthorizationCode").get("AuthorizationCode");
    var expiresTime = storages.create("expiresTime").get("expiresTime") == undefined ? "未激活" : storages.create("expiresTime").get("expiresTime");
    // 在主线程或UI线程中调用此方法
    var oldVersion;
    function getRemoteVersionInBackground(callback) {
        threads.start(function() {
            try {
                // 在新线程中执行耗时操作
                var result = pjysdk.GetRemoteVar("version");
                
                // 获取结果后通过回调返回给UI线程
                ui.run(() => {
                    if (result && result.result && result.result.value) {
                        var oldVersion = result.result.value;
                        callback(oldVersion, null);
                    } else {
                        callback(null, "获取版本信息失败");
                    }
                });
            } catch (e) {
                // 错误处理
                ui.run(() => {
                    callback(null, "发生异常1: " + e);
                });
            }
        });
    }
    
    // 使用示例
    getRemoteVersionInBackground(function(version, error) {
        if (error) {
            console.error(error);
            log(error);
        } else {
            ui.appVersion.setText(version);
            // 更新UI显示
        
        }
    });
    
    
    var myXmlPath = files.path("./res/layout/my.xml");
    
    
    pjysdk.SetCard(AuthorizationCode);
    pjysdk.event.on("heartbeat_failed", function(hret) {
        toastLog(hret.message);
        if (hret.code === 10214) {
            sleep(200);
            exit();  // 退出脚本
            toastLog("心跳失败1");
            return
        }
        log("心跳失败，尝试重登...")
        sleep(2000);
        let login_ret;
        var thread = threads.start(function() {
         login_ret = pjysdk.CardLogin();
        });
        thread.join();
        if (login_ret.code == 0) {
            log("重登成功");
        } else {
            toastLog(login_ret.message);  // 重登失败
            sleep(200);
            exit();  // 退出脚本
            toastLog("心跳失败2");
        }
    });
    function 添加qq(qq) {
    
            app.startActivity({
                action: "android.intent.action.VIEW",
                data: "mqqapi://card/show_pslcard?card_type=1&uin=" + qq,
                packageName: "com.tencent.mobileqq",
            })
    }  
        // 继承ui.Widget
        util.extend(IconWidget, ui.Widget);
        
        // 注册控件，命名为"icon2"
        ui.registerWidget("icon2", IconWidget);
        
    
    
    }
    setTimeout(function(){
    //更新
    {
        //检测更新
        var update = threads.start(function () {
        
            //更新文件
            //var cloudRenew = require("./script/dyzf/cloudRenew.js");
            //cloudRenew.cloudRenew();
        });



    // 要使用Android Resources的特性，需要在project.json中加上androidResources属性
    // 之后将推出项目模块功能，可以轻松地使用项目模块创建
    // 使用此语句，启用本脚本的使用安卓资源的特性
    ui.useAndroidResources();
    // 设置自定义主题
    activity.theme.applyStyle(ui.R.style.MainTheme, true);
    // 设置状态栏颜色为从xml获取的颜色
    ui.statusBarColor(activity.resources.getColor(ui.R.color.mainColorPrimaryDark));
    }
    //无界配置
    {
        if (storages.create("duoshaomiao").get("duoshaomiao")) {
            var duoshaomiao = storages.create("duoshaomiao").get("duoshaomiao")
        } else {
            var duoshaomiao = ""
        }
        if (storages.create("xinhun").get("xinhun")) {
            var xinhun = storages.create("xinhun").get("xinhun")
        } else {
            var xinhun = ""
        }
        if (storages.create("shunxu").get("shunxu")) {
            var shunxu = storages.create("shunxu").get("shunxu")
        } else {
            var shunxu = ""
        }
        if (storages.create("gangban").get("gangban")) {
            var gangban = storages.create("gangban").get("gangban")
        } else {
            var gangban = ""
        }
        if (storages.create("duoshan").get("duoshan")) {
            var duoshan = storages.create("duoshan").get("duoshan")
        } else {
            var duoshan = ""
        }
        if (storages.create("liandian").get("liandian")) {
            var liandian = storages.create("liandian").get("liandian")
        } else {
            var liandian = ""
        }
        if (storages.create("ticketKey").get("ticketKey")) {
            var ticketKey = storages.create("ticketKey").get("ticketKey")
        } else {
            var ticketKey = ""
        }
        if (storages.create("video").get("video") == undefined) {
            var video = "";
        } else {
            var video = storages.create("video").get("video");
        }
        if (storages.create("convention").get("convention") == undefined) {
            var convention = "";
        } else {
            var convention = storages.create("convention").get("convention");
        }
        if (storages.create("genuine").get("genuine") == undefined) {
            var genuine = "";
        } else {
            var genuine = storages.create("genuine").get("genuine");
        }
        if (storages.create("volcano").get("volcano") == undefined) {
            var volcano = "";
        } else {
            var volcano = storages.create("volcano").get("volcano");
        }
        if (storages.create("selected").get("selected") == undefined) {
            var selected = "";
        } else {
            var selected = storages.create("selected").get("selected");
        }
        if (storages.create("like").get("like") == undefined) {
            var like = "";
        } else {
            var like = storages.create("like").get("like");
        }
        if (storages.create("imageText").get("imageText") == undefined) {
            var imageText = "";
        } else {
            var imageText = storages.create("imageText").get("imageText");
        }
        if (storages.create("profilePicture").get("profilePicture") == undefined) {
            var profilePicture = "";
        } else {
            var profilePicture = storages.create("profilePicture").get("profilePicture");
        }
        if (storages.create("collect").get("collect") == undefined) {
            var collect = "";
        } else {
            var collect = storages.create("collect").get("collect");
        }
        if (storages.create("comment").get("comment") == undefined) {
            var comment = "";
        } else {
            var comment = storages.create("comment").get("comment");
        }
        if (storages.create("followWithInterest").get("followWithInterest") == undefined) {
            var followWithInterest = "";
        } else {
            var followWithInterest = storages.create("followWithInterest").get("followWithInterest");
        }
        if (storages.create("privateLetter").get("privateLetter") == undefined) {
            var privateLetter = "";
        } else {
            var privateLetter = storages.create("privateLetter").get("privateLetter");
        }
        if (storages.create("share").get("share") == undefined) {
            var share = "";
        } else {
            var share = storages.create("share").get("share");
        }
        if (storages.create("restartCount").get("restartCount") == undefined) {
            var restartCount = "";
        } else {
            var restartCount = storages.create("restartCount").get("restartCount");
        }
        if (storages.create("cgtl").get("cgtl") == undefined) {
            var cgtl = "";
        } else {
            var cgtl = storages.create("cgtl").get("cgtl");
        }
        if (storages.create("shoppingMall").get("shoppingMall") == undefined) {
            var shoppingMall = "";
        } else {
            var shoppingMall = storages.create("shoppingMall").get("shoppingMall");
        }
        if (storages.create("sharetl").get("sharetl") == undefined) {
            var sharetl = "";
        } else {
            var sharetl = storages.create("sharetl").get("sharetl");
        }
        if (storages.create("commenttl").get("commenttl") == undefined) {
            var commenttl = "";
        } else {
            var commenttl = storages.create("commenttl").get("commenttl");
        }
        if (storages.create("liketl").get("liketl") == undefined) {
            var liketl = "";
        } else {
            var liketl = storages.create("liketl").get("liketl");
        }
        if (storages.create("privateLettertl").get("privateLettertl") == undefined) {
            var privateLettertl = "";
        } else {
            var privateLettertl = storages.create("privateLettertl").get("privateLettertl");
        }
        if (storages.create("followWithInteresttl").get("followWithInteresttl") == undefined) {
            var followWithInteresttl = "";
        } else {
            var followWithInteresttl = storages.create("followWithInteresttl").get("followWithInteresttl");
        }
        if (storages.create("collecttl").get("collecttl") == undefined) {
            var collecttl = "";
        } else {
            var collecttl = storages.create("collecttl").get("collecttl");
        }
        if (storages.create("profilePicturetl").get("profilePicturetl") == undefined) {
            var profilePicturetl = "";
        } else {
            var profilePicturetl = storages.create("profilePicturetl").get("profilePicturetl");
        }
        if (storages.create("imageTexttl").get("imageTexttl") == undefined) {
            var imageTexttl = "";
        } else {
            var imageTexttl = storages.create("imageTexttl").get("imageTexttl");
        }
        if (storages.create("backSpeed").get("backSpeed") == undefined) {
            var backSpeed = "";
        } else {
            var backSpeed = storages.create("backSpeed").get("backSpeed");
        }
        if (storages.create("delay").get("delay") == undefined) {
            var delay = "";
        } else {
            var delay = storages.create("delay").get("delay");
        }
        if (storages.create("clickComment").get("clickComment") == undefined) {
            var clickComment = "";
        } else {
            var clickComment = storages.create("clickComment").get("clickComment");
        }
        if (storages.create("clickCommenttl").get("clickCommenttl") == undefined) {
            var clickCommenttl = "";
        } else {
            var clickCommenttl = storages.create("clickCommenttl").get("clickCommenttl");
        }
        if (storages.create("search").get("search") == undefined) {
            var search = "";
        } else {
            var search = storages.create("search").get("search");
        }
        if (storages.create("switchLike").get("switchLike") == undefined) {
            var switchLike = "";
        } else {
            var switchLike = storages.create("switchLike").get("switchLike");
        }
        if (storages.create("switchImageText").get("switchImageText") == undefined) {
            var switchImageText = "";
        } else {
            var switchImageText = storages.create("switchImageText").get("switchImageText");
        }
        if (storages.create("switchProfilePicture").get("switchProfilePicture") == undefined) {
            var switchProfilePicture = "";
        } else {
            var switchProfilePicture = storages.create("switchProfilePicture").get("switchProfilePicture");
        }
        if (storages.create("switchCollect").get("switchCollect") == undefined) {
            var switchCollect = "";
        } else {
            var switchCollect = storages.create("switchCollect").get("switchCollect");
        }
        if (storages.create("switchComment").get("switchComment") == undefined) {
            var switchComment = "";
        } else {
            var switchComment = storages.create("switchComment").get("switchComment");
        }
        if (storages.create("switchFollowWithInterest").get("switchFollowWithInterest") == undefined) {
            var switchFollowWithInterest = "";
        } else {
            var switchFollowWithInterest = storages.create("switchFollowWithInterest").get("switchFollowWithInterest");
        }
        if (storages.create("switchPrivateLetter").get("switchPrivateLetter") == undefined) {
            var switchPrivateLetter = "";
        } else {
            var switchPrivateLetter = storages.create("switchPrivateLetter").get("switchPrivateLetter");
        }
        if (storages.create("switchShare").get("switchShare") == undefined) {
            var switchShare = "";
        } else {
            var switchShare = storages.create("switchShare").get("switchShare");
        }
        if (storages.create("switchClickComment").get("switchClickComment") == undefined) {
            var switchClickComment = "";
        } else {
            var switchClickComment = storages.create("switchClickComment").get("switchClickComment");
        }
        if (storages.create("closure").get("closure") == undefined) {
            var closure = "";
        } else {
            var closure = storages.create("closure").get("closure");
        }
        if (storages.create("privateMessageDrai").get("privateMessageDrai") == undefined) {
            var privateMessageDrai = "";
        } else {
            var privateMessageDrai = storages.create("privateMessageDrai").get("privateMessageDrai");
        }
        if (storages.create("fenshai").get("fenshai") == undefined) {
            var fenshai = "";
        } else {
            var fenshai = storages.create("fenshai").get("fenshai");
        }
        if (storages.create("guanshai").get("guanshai") == undefined) {
            var guanshai = "";
        } else {
            var guanshai = storages.create("guanshai").get("guanshai");
        }
        if (storages.create("removeGIF").get("removeGIF") == undefined) {
            var removeGIF = "";
        } else {
            var removeGIF = storages.create("removeGIF").get("removeGIF");
        }
        if (storages.create("guanzhushu").get("guanzhushu") == undefined) {
            var guanzhushu = "";
        } else {
            var guanzhushu = storages.create("guanzhushu").get("guanzhushu");
        }
        if (storages.create("fensishu").get("fensishu") == undefined) {
            var fensishu = "";
        } else {
            var fensishu = storages.create("fensishu").get("fensishu");
        }
        if (storages.create("zuopinshu").get("zuopinshu") == undefined) {
            var zuopinshu = "";
        } else {
            var zuopinshu = storages.create("zuopinshu").get("zuopinshu");
        }
        if (storages.create("xingbie").get("xingbie") == undefined) {
            var xingbie = "";
        } else {
            var xingbie = storages.create("xingbie").get("xingbie");
        }
        if (storages.create("wuxiaoshu").get("wuxiaoshu") == undefined) {
            var wuxiaoshu = "";
        } else {
            var wuxiaoshu = storages.create("wuxiaoshu").get("wuxiaoshu");
        }
        if (storages.create("hanger").get("hanger") == undefined) {
            var hanger = "";
        } else {
            var hanger = storages.create("hanger").get("hanger");
        }
        if (storages.create("separate").get("separate") == undefined) {
            var separate = "";
        } else {
            var separate = storages.create("separate").get("separate");
        }
        if (storages.create("selfSearching").get("selfSearching") == undefined) {
            var selfSearching = "";
        } else {
            var selfSearching = storages.create("selfSearching").get("selfSearching");
        }
        if (storages.create("sixincishu").get("sixincishu") == undefined) {
            var sixincishu = "";
        } else {
            var sixincishu = storages.create("sixincishu").get("sixincishu");
        }
        if (storages.create("youxu").get("youxu") == undefined) {
            var youxu = "";
        } else {
            var youxu = storages.create("youxu").get("youxu");
        }
        if (storages.create("youxu1").get("youxu1") == undefined) {
            var youxu1 = "";
        } else {
            var youxu1 = storages.create("youxu1").get("youxu1");
        }
        if (storages.create("youxu2").get("youxu2") == undefined) {
            var youxu2 = "";
        } else {
            var youxu2 = storages.create("youxu2").get("youxu2");
        }
        if (storages.create("youxu3").get("youxu3") == undefined) {
            var youxu3 = "";
        } else {
            var youxu3 = storages.create("youxu3").get("youxu3");
        }
        if (storages.create("youxu4").get("youxu4") == undefined) {
            var youxu4 = "";
        } else {
            var youxu4 = storages.create("youxu4").get("youxu4");
        }
        if (storages.create("youxu5").get("youxu5") == undefined) {
            var youxu5 = "";
        } else {
            var youxu5 = storages.create("youxu5").get("youxu5");
        }
        if (storages.create("youxu6").get("youxu6") == undefined) {
            var youxu6 = "";
        } else {
            var youxu6 = storages.create("youxu6").get("youxu6");
        }
        if (storages.create("youxu7").get("youxu7") == undefined) {
            var youxu7 = "";
        } else {
            var youxu7 = storages.create("youxu7").get("youxu7");
        }
        if (storages.create("youxu8").get("youxu8") == undefined) {
            var youxu8 = "";
        } else {
            var youxu8 = storages.create("youxu8").get("youxu8");
        }
        if (storages.create("youxu9").get("youxu9") == undefined) {
            var youxu9 = "";
        } else {
            var youxu9 = storages.create("youxu9").get("youxu9");
        }
        if (storages.create("youxu10").get("youxu10") == undefined) {
            var youxu10 = "";
        } else {
            var youxu10 = storages.create("youxu10").get("youxu10");
        }
        if (storages.create("youxu11").get("youxu11") == undefined) {
            var youxu11 = "";
        } else {
            var youxu11 = storages.create("youxu11").get("youxu11");
        }
        if (storages.create("youxu12").get("youxu12") == undefined) {
            var youxu12 = "";
        } else {
            var youxu12 = storages.create("youxu12").get("youxu12");
        }
        if (storages.create("youxu13").get("youxu13") == undefined) {
            var youxu13 = "";
        } else {
            var youxu13 = storages.create("youxu13").get("youxu13");
        }
        if (storages.create("youxu14").get("youxu14") == undefined) {
            var youxu14 = "";
        } else {
            var youxu14 = storages.create("youxu14").get("youxu14");
        }
        if (storages.create("youxu15").get("youxu15") == undefined) {
            var youxu15 = "";
        } else {
            var youxu15 = storages.create("youxu15").get("youxu15");
        }
        if (storages.create("wenping").get("wenping") == undefined) {
            var wenping = "";
        } else {
            var wenping = storages.create("wenping").get("wenping");
        }
        if (storages.create("zanyan").get("zanyan") == undefined) {
            var zanyan = "";
        } else {
            var zanyan = storages.create("zanyan").get("zanyan");
        }
        if (storages.create("meiyunxing").get("meiyunxing") == undefined) {
            var meiyunxing = "";
        } else {
            var meiyunxing = storages.create("meiyunxing").get("meiyunxing");
        }
        if (storages.create("xiuxi").get("xiuxi") == undefined) {
            var xiuxi = "";
        } else {
            var xiuxi = storages.create("xiuxi").get("xiuxi");
        }
        if (storages.create("rest").get("rest") == undefined) {
            var rest = "";
        } else {
            var rest = storages.create("rest").get("rest");
        }
        var once = 0;
        if (storages.create("baiping").get("baiping") == undefined) {
            var baiping = "";
        } else {
            var baiping = storages.create("baiping").get("baiping");
        }
        if (storages.create("jiance").get("jiance") == undefined) {
            var jiance = "";
        } else {
            var jiance = storages.create("jiance").get("jiance");
        }
        if (storages.create("lianxu").get("lianxu") == undefined) {
            var lianxu = "";
        } else {
            var lianxu = storages.create("lianxu").get("lianxu");
        }
        if (storages.create("jiangxiuxi").get("jiangxiuxi") == undefined) {
            var jiangxiuxi = "";
        } else {
            var jiangxiuxi = storages.create("jiangxiuxi").get("jiangxiuxi");
        }
        if (storages.create("qishui").get("qishui") == undefined) {
            var qishui = "";
        } else {
            var qishui = storages.create("qishui").get("qishui");
        }
        if (storages.create("xihuan").get("xihuan") == undefined) {
            var xihuan = "";
        } else {
            var xihuan = storages.create("xihuan").get("xihuan");
        }
        if (storages.create("lingdian").get("lingdian") == undefined) {
            var lingdian = "";
        } else {
            var lingdian = storages.create("lingdian").get("lingdian");
        }
        if (storages.create("yidian").get("yidian") == undefined) {
            var yidian = "";
        } else {
            var yidian = storages.create("yidian").get("yidian");
        }
        if (storages.create("erdian").get("erdian") == undefined) {
            var erdian = "";
        } else {
            var erdian = storages.create("erdian").get("erdian");
        }
        if (storages.create("sandian").get("sandian") == undefined) {
            var sandian = "";
        } else {
            var sandian = storages.create("sandian").get("sandian");
        }
        if (storages.create("sidian").get("sidian") == undefined) {
            var sidian = "";
        } else {
            var sidian = storages.create("sidian").get("sidian");
        }
        if (storages.create("wudian").get("wudian") == undefined) {
            var wudian = "";
        } else {
            var wudian = storages.create("wudian").get("wudian");
        }
        if (storages.create("liudian").get("liudian") == undefined) {
            var liudian = "";
        } else {
            var liudian = storages.create("liudian").get("liudian");
        }
        if (storages.create("qidian").get("qidian") == undefined) {
            var qidian = "";
        } else {
            var qidian = storages.create("qidian").get("qidian");
        }
        if (storages.create("badian").get("badian") == undefined) {
            var badian = "";
        } else {
            var badian = storages.create("badian").get("badian");
        }
        if (storages.create("jiudian").get("jiudian") == undefined) {
            var jiudian = "";
        } else {
            var jiudian = storages.create("jiudian").get("jiudian");
        }
        if (storages.create("shidian").get("shidian") == undefined) {
            var shidian = "";
        } else {
            var shidian = storages.create("shidian").get("shidian");
        }
        if (storages.create("shiyidian").get("shiyidian") == undefined) {
            var shiyidian = "";
        } else {
            var shiyidian = storages.create("shiyidian").get("shiyidian");
        }
        if (storages.create("shierdian").get("shierdian") == undefined) {
            var shierdian = "";
        } else {
            var shierdian = storages.create("shierdian").get("shierdian");
        }
        if (storages.create("shisandian").get("shisandian") == undefined) {
            var shisandian = "";
        } else {
            var shisandian = storages.create("shisandian").get("shisandian");
        }
        if (storages.create("shisidian").get("shisidian") == undefined) {
            var shisidian = "";
        } else {
            var shisidian = storages.create("shisidian").get("shisidian");
        }
        if (storages.create("shiwudian").get("shiwudian") == undefined) {
            var shiwudian = "";
        } else {
            var shiwudian = storages.create("shiwudian").get("shiwudian");
        }
        if (storages.create("shiliudian").get("shiliudian") == undefined) {
            var shiliudian = "";
        } else {
            var shiliudian = storages.create("shiliudian").get("shiliudian");
        }
        if (storages.create("shiqidian").get("shiqidian") == undefined) {
            var shiqidian = "";
        } else {
            var shiqidian = storages.create("shiqidian").get("shiqidian");
        }
        if (storages.create("shibadian").get("shibadian") == undefined) {
            var shibadian = "";
        } else {
            var shibadian = storages.create("shibadian").get("shibadian");
        }
        if (storages.create("shijiudian").get("shijiudian") == undefined) {
            var shijiudian = "";
        } else {
            var shijiudian = storages.create("shijiudian").get("shijiudian");
        }
        if (storages.create("ershidian").get("ershidian") == undefined) {
            var ershidian = "";
        } else {
            var ershidian = storages.create("ershidian").get("ershidian");
        }
        if (storages.create("ershiyidian").get("ershiyidian") == undefined) {
            var ershiyidian = "";
        } else {
            var ershiyidian = storages.create("ershiyidian").get("ershiyidian");
        }
        if (storages.create("ershierdian").get("ershierdian") == undefined) {
            var ershierdian = "";
        } else {
            var ershierdian = storages.create("ershierdian").get("ershierdian");
        }
        if (storages.create("ershisandian").get("ershisandian") == undefined) {
            var ershisandian = "";
        } else {
            var ershisandian = storages.create("ershisandian").get("ershisandian");
        }
        if (storages.create("fuwuqidizhi").get("fuwuqidizhi") == undefined) {
            var fuwuqidizhi = "";
        } else {
            var fuwuqidizhi = storages.create("fuwuqidizhi").get("fuwuqidizhi");
        }
        if (storages.create("ceshi").get("ceshi") == undefined) {
            var ceshi = "";
        } else {
            var ceshi = storages.create("ceshi").get("ceshi");
        }
        if (storages.create("liuhen").get("liuhen") == undefined) {
            var liuhen = "";
        } else {
            var liuhen = storages.create("liuhen").get("liuhen");
        }
        if (storages.create("liunan").get("liunan") == undefined) {
            var liunan = "";
        } else {
            var liunan = storages.create("liunan").get("liunan");
        }
        if (storages.create("liunv").get("liunv") == undefined) {
            var liunv = "";
        } else {
            var liunv = storages.create("liunv").get("liunv");
        }
        if (storages.create("mix1").get("mix1") == undefined) {
            var mix1 = "";
        } else {
            var mix1 = storages.create("mix1").get("mix1");
        }
        if (storages.create("hunyimoshixia").get("hunyimoshixia") == undefined) {
            var hunyimoshixia = "";
        } else {
            var hunyimoshixia = storages.create("hunyimoshixia").get("hunyimoshixia");
        }
        if (storages.create("huoli").get("huoli") == undefined) {
            var huoli = "";
        } else {
            var huoli = storages.create("huoli").get("huoli");
        }
        if (storages.create("xinyouxu").get("xinyouxu") == undefined) {
            var xinyouxu = "";
        } else {
            var xinyouxu = storages.create("xinyouxu").get("xinyouxu");
        }
    };
    


//ui库

{

// 加载主页框架
ui.layoutFile("main");

//无界ui
ui.inflate(
    <scroll>
        <vertical bg="#E8E8E8">

            <card margin="5">
                <vertical>
                    <card>
                        <text text="权限" textSize="15sp" textColor="black" gravity="center" padding="8 8 8 8" />
                    </card>
                    <vertical gravity="center">
                        <Switch id="autoService" text="无障碍" checked="{{auto.service != null}}" padding="8 8 8 8" textSize="15sp" />
                        <Switch id="floatingWindow" text="悬浮窗" checked="{{floaty.checkPermission() != false}}" padding="8 8 8 8" textSize="15sp" />
                    </vertical>
                </vertical>
            </card>
            <card margin="5" h="auto">
                <vertical>

                    <card>
                        <text text="对标库" textSize="15sp" textColor="black" gravity="center" padding="8 3" />
                    </card>
                    
                    <vertical>
                    
                        <spinner id="mySpinner" entries="{{entries}}" padding="8 8 8 12"/>
                    </vertical>  
                    
                </vertical>
            </card>
            <card margin="5" h="auto">
                <vertical>
                    <card>
                        <text text="功能" textSize="15sp" textColor="black" gravity="center" padding="8 3" />
                    </card>
                    <horizontal gravity="center">
                        <vertical layout_weight="1">
                            <text text="常规" textColor="black"
                                textSize="15sp" padding="8 8 8 8" />
                        </vertical>
                        <checkbox id="convention" checked={convention} />
                    </horizontal>
                    <horizontal gravity="center">
                        <vertical layout_weight="1">
                            <text text="视频" textColor="black"
                                textSize="15sp" padding="8 8 8 8" />
                        </vertical>
                        <checkbox id="video" checked={video} />
                    </horizontal>
                </vertical>
            </card>
            <card margin="5" h="auto">
                <vertical bg="#E8E8E8">
                <card margin="5" h="auto">
    <vertical>
     
        <card marginBottom="8">
            <text text="配置" textSize="15sp" textColor="black" gravity="center" padding="8 3" />
        </card>

     
        <horizontal gravity="center" marginBottom="10">
            <card id="conventionConfig" w="70" h="27" marginRight="8" cardCornerRadius="5" cardElevation="0" foreground="?selectableItemBackground">
                <View bg="#373bd3" />
                <text textColor="#ffffff" gravity="center_vertical|center_horizontal">常规</text>
            </card>
            
            <card id="huoliConfig" w="70" h="27" marginRight="8" cardCornerRadius="5" cardElevation="0" foreground="?selectableItemBackground">
                <View bg="#373bd3" />
                <text textColor="#ffffff" gravity="center_vertical|center_horizontal">火力</text>
            </card>
            
            <card id="youxuConfig" w="70" h="27" marginRight="8" cardCornerRadius="5" cardElevation="0" foreground="?selectableItemBackground">
                <View bg="#373bd3" />
                <text textColor="#ffffff" gravity="center_vertical|center_horizontal">有序</text>
            </card>
            
            <card id="qishuiyinyueConfig" w="70" h="27" marginRight="0" cardCornerRadius="5" cardElevation="0" foreground="?selectableItemBackground">
                <View bg="#373bd3" />
                <text textColor="#ffffff" gravity="center_vertical|center_horizontal">汽水</text>
            </card>
        </horizontal>

        
        <horizontal gravity="center" marginBottom="10">
            <card id="xihuanConfig" w="70" h="27" marginRight="8" cardCornerRadius="5" cardElevation="0" foreground="?selectableItemBackground">
                <View bg="#373bd3" />
                <text textColor="#ffffff" gravity="center_vertical|center_horizontal">喜欢</text>
            </card>
            
            <card id="imageTextConfig" w="70" h="27" marginRight="8" cardCornerRadius="5" cardElevation="0" foreground="?selectableItemBackground">
                <View bg="#373bd3" />
                <text textColor="#ffffff" gravity="center_vertical|center_horizontal">港版</text>
            </card>
            
            <card id="fengzanConfig" w="70" h="27" marginRight="8" cardCornerRadius="5" cardElevation="0" foreground="?selectableItemBackground">
                <View bg="#373bd3" />
                <text textColor="#ffffff" gravity="center_vertical|center_horizontal">封赞</text>
            </card>
            
            <card id="liuhenConfig" w="70" h="27" marginRight="0" cardCornerRadius="5" cardElevation="0" foreground="?selectableItemBackground">
                <View bg="#373bd3" />
                <text textColor="#ffffff" gravity="center_vertical|center_horizontal">留痕</text>
            </card>
        </horizontal>

        
        <horizontal gravity="center" marginTop="5" marginBottom="10">
            <card id="clearTask" w="300" h="40" cardCornerRadius="5" cardElevation="0" foreground="?selectableItemBackground">
                <View bg="#373bd3" />
                <text textColor="#ffffff" gravity="center_vertical|center_horizontal">清空</text>
            </card>
        </horizontal>
    </vertical>
</card>



                    <card margin="5" h="auto">
                        <vertical>
                            <card>
                                <text text="任务" textSize="15sp" textColor="black" gravity="center" padding="8 3" />
                            </card>
                            <horizontal gravity="center">
                                <text text="点赞" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                <input id="like" textSize="15sp" text={like} />
                                <text text="次" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                <text text="图文" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                <input id="imageText" textSize="15sp" text={imageText} />
                                <text text="次" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                <text text="头像" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                <input id="profilePicture" textSize="15sp" text={profilePicture} />
                                <text text="次" textColor="black" textSize="15sp" padding="8 8 8 8" />
                            </horizontal>
                            <horizontal gravity="center">
                                <checkbox id="switchLike" text="点赞" checked={switchLike} />
                                <checkbox id="switchImageText" text="图文" checked={switchImageText} />
                                <checkbox id="switchProfilePicture" text="头像" checked={switchProfilePicture} />
                            </horizontal>
                            <horizontal gravity="center">
                                <text text="收藏" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                <input id="collect" textSize="15sp" text={collect} />
                                <text text="次" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                <text text="评论" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                <input id="comment" textSize="15sp" text={comment} />
                                <text text="次" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                <text text="关注" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                <input id="followWithInterest" textSize="15sp" text={followWithInterest} />
                                <text text="次" textColor="black" textSize="15sp" padding="8 8 8 8" />
                            </horizontal>
                            <horizontal gravity="center">
                                <checkbox id="switchCollect" text="收藏" checked={switchCollect} />
                                <checkbox id="switchComment" text="评论" checked={switchComment} />
                                <checkbox id="switchFollowWithInterest" text="关注" checked={switchFollowWithInterest} />
                            </horizontal>
                            <horizontal gravity="center">
                                <text text="私信" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                <input id="privateLetter" textSize="15sp" text={privateLetter} />
                                <text text="次" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                <text text="分享" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                <input id="share" textSize="15sp" text={share} />
                                <text text="次" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                <text text="点评" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                <input id="clickComment" textSize="15sp" text={clickComment} />
                                <text text="次" textColor="black" textSize="15sp" padding="8 8 8 8" />
                            </horizontal>
                            <horizontal gravity="center">
                                <checkbox id="switchPrivateLetter" text="私信" checked={switchPrivateLetter} />
                                <checkbox id="switchShare" text="分享" checked={switchShare} />
                                <checkbox id="switchClickComment" text="点评" checked={switchClickComment} />
                            </horizontal>
                            <horizontal gravity="center">
                                <text text="重启" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                <input id="restartCount" textSize="15sp" text={restartCount} />
                                <text text="次" textColor="black" textSize="15sp" padding="8 8 8 8" />
                            </horizontal>
                            <horizontal gravity="center">
                                <text text="每运行" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                <input id="meiyunxing" textSize="15sp" text={meiyunxing} />
                                <text text="分钟" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                <text text="休息" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                <input id="xiuxi" textSize="15sp" text={xiuxi} />
                                <text text="分钟" textColor="black" textSize="15sp" padding="8 8 8 8" />
                            </horizontal>
                            <horizontal gravity="center">
                                <text text="连续" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                <input id="lianxu" textSize="15sp" text={lianxu} />
                                <text text="次点赞失败" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                <text text="将休息" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                <input id="jiangxiuxi" textSize="15sp" text={jiangxiuxi} />
                                <text text="分钟" textColor="black" textSize="15sp" padding="8 8 8 8" />
                            </horizontal>
                            <horizontal gravity="center">
                                <text text="每" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                <input id="duoshaomiao" textSize="15sp" text={duoshaomiao} />
                                <text text="秒做一次任务" textColor="black" textSize="15sp" padding="8 8 8 8" />
                            </horizontal>
                        </vertical>
                    </card>

                    <card margin="5" h="auto">
                        <vertical>
                            <card>
                                <text text="停留" textSize="15sp" textColor="black" gravity="center" padding="8 3" />
                            </card>
                            <horizontal gravity="center">
                                <horizontal gravity="center">
                                    <text text="常规停留" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                    <input id="cgtl" textSize="15sp" text={cgtl} />
                                    <text text="秒" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                </horizontal>
                                <horizontal gravity="center">
                                    <text text="点赞停留" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                    <input id="liketl" textSize="15sp" text={liketl} />
                                    <text text="秒" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                </horizontal>
                            </horizontal>
                            <horizontal gravity="center">
                                <horizontal gravity="center">
                                    <text text="图文停留" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                    <input id="imageTexttl" textSize="15sp" text={imageTexttl} />
                                    <text text="秒" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                </horizontal>
                                <horizontal gravity="center">
                                    <text text="头像停留" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                    <input id="profilePicturetl" textSize="15sp" text={profilePicturetl} />
                                    <text text="秒" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                </horizontal>
                            </horizontal>
                            <horizontal gravity="center">
                                <horizontal gravity="center">
                                    <text text="收藏停留" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                    <input id="collecttl" textSize="15sp" text={collecttl} />
                                    <text text="秒" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                </horizontal>
                                <horizontal gravity="center">
                                    <text text="评论停留" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                    <input id="commenttl" textSize="15sp" text={commenttl} />
                                    <text text="秒" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                </horizontal>
                            </horizontal>
                            <horizontal gravity="center">
                                <horizontal gravity="center">
                                    <text text="关注停留" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                    <input id="followWithInteresttl" textSize="15sp" text={followWithInteresttl} />
                                    <text text="秒" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                </horizontal>
                                <horizontal gravity="center">
                                    <text text="私信停留" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                    <input id="privateLettertl" textSize="15sp" text={privateLettertl} />
                                    <text text="秒" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                </horizontal>
                            </horizontal>
                            <horizontal gravity="center">
                                <text text="分享停留" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                <input id="sharetl" textSize="15sp" text={sharetl} />
                                <text text="秒" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                <text text="点评停留" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                <input id="clickCommenttl" textSize="15sp" text={clickCommenttl} />
                                <text text="秒" textColor="black" textSize="15sp" padding="8 8 8 8" />
                            </horizontal>
                            <horizontal gravity="center">
                                <text text="返回停留" textColor="black" textSize="15sp" padding="8 8 8 8" />
                                <input id="backSpeed" textSize="15sp" text={backSpeed} />
                                <text text="秒" textColor="black" textSize="15sp" padding="8 8 8 8" />
                            </horizontal>
                        </vertical>
                    </card>

                    <card margin="5" h="auto">
                        <vertical>
                            <card>
                                <text text="在线" textSize="15sp" textColor="black" gravity="center" padding="8 3" />
                            </card>
                            <horizontal gravity="center">
                                <checkbox id="lingdian" checked={lingdian} />
                                <text text="0  点" textColor="black" textSize="15sp" />
                                <checkbox id="yidian" checked={yidian} />
                                <text text="1  点" textColor="black" textSize="15sp" />
                                <checkbox id="erdian" checked={erdian} />
                                <text text="2  点" textColor="black" textSize="15sp" />
                                <checkbox id="sandian" checked={sandian} />
                                <text text="3  点" textColor="black" textSize="15sp" />
                            </horizontal>
                            <horizontal gravity="center">

                                <checkbox id="sidian" checked={sidian} />
                                <text text="4  点" textColor="black" textSize="15sp" />

                                <checkbox id="wudian" checked={wudian} />
                                <text text="5  点" textColor="black" textSize="15sp" />

                                <checkbox id="liudian" checked={liudian} />
                                <text text="6  点" textColor="black" textSize="15sp" />

                                <checkbox id="qidian" checked={qidian} />
                                <text text="7  点" textColor="black" textSize="15sp" />
                            </horizontal>
                            <horizontal gravity="center">

                                <checkbox id="badian" checked={badian} />
                                <text text="8  点" textColor="black" textSize="15sp" />

                                <checkbox id="jiudian" checked={jiudian} />
                                <text text="9点  " textColor="black" textSize="15sp" />

                                <checkbox id="shidian" checked={shidian} />
                                <text text="10点" textColor="black" textSize="15sp" />

                                <checkbox id="shiyidian" checked={shiyidian} />
                                <text text="11点" textColor="black" textSize="15sp" />
                            </horizontal>
                            <horizontal gravity="center">

                                <checkbox id="shierdian" checked={shierdian} />
                                <text text="12点" textColor="black" textSize="15sp" />

                                <checkbox id="shisandian" checked={shisandian} />
                                <text text="13点" textColor="black" textSize="15sp" />

                                <checkbox id="shisidian" checked={shisidian} />
                                <text text="14点" textColor="black" textSize="15sp" />

                                <checkbox id="shiwudian" checked={shiwudian} />
                                <text text="15点" textColor="black" textSize="15sp" />
                            </horizontal>
                            <horizontal gravity="center">

                                <checkbox id="shiliudian" checked={shiliudian} />
                                <text text="16点" textColor="black" textSize="15sp" />

                                <checkbox id="shiqidian" checked={shiqidian} />
                                <text text="17点" textColor="black" textSize="15sp" />

                                <checkbox id="shibadian" checked={shibadian} />
                                <text text="18点" textColor="black" textSize="15sp" />

                                <checkbox id="shijiudian" checked={shijiudian} />
                                <text text="19点" textColor="black" textSize="15sp" />
                            </horizontal>
                            <horizontal gravity="center">

                                <checkbox id="ershidian" checked={ershidian} />
                                <text text="20点" textColor="black" textSize="15sp" />

                                <checkbox id="ershiyidian" checked={ershiyidian} />
                                <text text="21点" textColor="black" textSize="15sp" />

                                <checkbox id="ershierdian" checked={ershierdian} />
                                <text text="22点" textColor="black" textSize="15sp" />
                                <checkbox id="ershisandian" checked={ershisandian} />
                                <text text="23点" textColor="black" textSize="15sp" />
                            </horizontal>
                        </vertical>
                    </card>

                </vertical>
            </card>
            <card margin="5" h="auto">
                <vertical >
                    <card>
                        <text text="平台" textSize="15sp" textColor="black" gravity="center" padding="8 3" />
                    </card>

                    <horizontal gravity="center">
                        <checkbox id="genuine" checked={genuine} />
                        <text text="正版" textColor="black" textSize="15sp" />

                        <checkbox id="volcano" checked={volcano} />
                        <text text="火山" textColor="black" textSize="15sp" />

                        <checkbox id="selected" checked={selected} />
                        <text text="精选" textColor="black" textSize="15sp" />

                        <checkbox id="shoppingMall" checked={shoppingMall} />
                        <text text="商城" textColor="black" textSize="15sp" />
                    </horizontal>

                    <horizontal gravity="center">
                        <checkbox id="search" checked={search} />
                        <text text="搜索" textColor="black" textSize="15sp" />

                        <checkbox id="qishui" checked={qishui} />
                        <text text="汽水" textColor="black" textSize="15sp" />

                        <checkbox id="duoshan" checked={duoshan} />
                        <text text="多闪" textColor="black" textSize="15sp" />

                        <checkbox id="gangban" checked={gangban} />
                        <text text="港版" textColor="black" textSize="15sp" />
                    </horizontal>

                </vertical>
            </card>
            <card margin="5" h="auto">
                <vertical>
                    <card>
                        <text text="其他" textSize="15sp" textColor="black" gravity="center" padding="8 3" />
                    </card>


                    <horizontal gravity="center">
                        <checkbox id="selfSearching" checked={selfSearching} />
                        <text text="自寻" textColor="black" textSize="15sp" />

                        <checkbox id="delay" checked={delay} />
                        <text text="延迟" textColor="black" textSize="15sp" />

                        <checkbox id="rest" checked={rest} />
                        <text text="休息" textColor="black" textSize="15sp" />

                        <checkbox id="baiping" checked={baiping} />
                        <text text="不点" textColor="black" textSize="15sp" />

                    </horizontal>

                    <horizontal gravity="center">

                        <checkbox id="jiance" checked={jiance} />
                        <text text="检测" textColor="black" textSize="15sp" />

                        <checkbox id="liuhen" checked={liuhen} />
                        <text text="留痕" textColor="black" textSize="15sp" />

                        <checkbox id="liunan" checked={liunan} />
                        <text text="留男" textColor="black" textSize="15sp" />

                        <checkbox id="liunv" checked={liunv} />
                        <text text="留女" textColor="black" textSize="15sp" />
                    </horizontal>

                    <horizontal gravity="center">

                        <checkbox id="zanyan" checked={zanyan} />
                        <text text="随机" textColor="black" textSize="15sp" />

                        <checkbox id="xinyouxu" checked={xinyouxu} />
                        <text text="有序" textColor="black" textSize="15sp" />

                        <checkbox id="huoli" checked={huoli} />
                        <text text="火力" textColor="black" textSize="15sp" />

                        <checkbox id="shunxu" checked={shunxu} />
                        <text text="顺序" textColor="black" textSize="15sp" />
                    </horizontal>

                    <horizontal gravity="center">
                        <checkbox id="xihuan" checked={xihuan} />
                        <text text="喜欢" textColor="black" textSize="15sp" />
                        <checkbox id="xinhun" checked={xinhun} />
                        <text text="新混" textColor="black" textSize="15sp" />
                    </horizontal>
                </vertical>
            </card>
            <button id="start" text="开始" style="Widget.AppCompat.Button.Colored" />
        </vertical >
    </scroll >
,  // 传入 XML 文件内容的字符串
ui.viewPager,  // 父视图
true  // 是否自动添加到父视图（可选，默认 false）
);
/*
let subView = ui.inflate(
    <vertical w="*" h="*" ><viewpager marginTop="50" id="viewpager_img" w="*" h="{{view_hei()}}px" ><img-layout h="auto" data="{{data.data.length-1}}" /><img-layout data="{{0}}" /><img-layout data="{{1}}" /><img-layout data="{{2}}" /><img-layout data="{{0}}" /></viewpager><tabs-layout marginTop="-30" data="" layout_gravity="center_horizontal" /><horizontal w="*" margin="20 50 20 0" ><horizontal layout_weight="1" w="*"><text text="图片切换时间" /><input w="100" id="time" text="{{data.time}}" /></horizontal><button layout_weight="1" w="*" id="open" text="开启轮播" /></horizontal></vertical>,  // 传入 XML 文件内容的字符串
    ui.viewPager,  // 父视图
    true  // 是否自动添加到父视图（可选，默认 false）
  );
  */
//主页ui
ui.inflate(
    <vertical w="*" h="*"  bg="#eee8f4" xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
        <viewpager marginTop="5" id="viewpager_img" w="*" h="{{view_hei()}}px" >
        <img-layout h="auto"  data="{{data.data.length-1}}" />
        //再此处添加所有轮播图片信息,照下方复制也可以
            <img-layout data="{{0}}" />
            <img-layout data="{{1}}" />
            <img-layout data="{{2}}" />


            //最后添加一个 第一个图片的页面 不知道为什么的请看说明实践方法
        <img-layout data="{{0}}" />
        </viewpager>
        <tabs-layout marginTop="-30" data="" layout_gravity="center_horizontal" />
        <androidx.core.widget.NestedScrollView
        android:id="@+id/contentScroll"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/tabs"
        marginTop="30"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

 <LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

<com.google.android.material.textview.MaterialTextView
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:text="No bounds."
    android:textSize="24sp"
    android:textColor="#333333"
    android:textStyle="bold"
    android:layout_marginLeft="16dp"  
    android:layout_marginTop="20dp" 
    android:layout_marginBottom="16dp"      
    app:fontFamily="sans-serif-medium"
    android:letterSpacing="0.01"    
    android:lineSpacingExtra="4dp"/> 
    <com.google.android.material.card.MaterialCardView
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="#FFFFFF"
    app:cardCornerRadius="12dp"
    app:cardElevation="1dp"
    app:strokeColor="#DDDDDD"
    app:strokeWidth="1dp"
    android:layout_marginBottom="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <com.google.android.material.textview.MaterialTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="授权验证"
                android:textSize="18sp"
                android:textColor="#333333"
                app:fontFamily="sans-serif-medium"/>

            <Space
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1"/>

            <com.google.android.material.imageview.ShapeableImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
               
                app:tint="#333333"/>
        </LinearLayout>


        <com.google.android.material.textview.MaterialTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="请输入您的授权码以验证身份"
            android:textSize="14sp"
            android:textColor="#666666"/>


<FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp">

            <EditText
                android:id="@+id/AuthorizationCode"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:hint="请输入授权码"
                android:inputType="textPassword"
                android:textColor="#333333"
                android:textSize="16sp"
                android:padding="12dp"/>
        </FrameLayout>


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            
            android:paddingRight="12dp"
            android:paddingBottom="8dp"
            android:paddingTop="16dp">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/verifyButton"
                android:layout_width="80dp"
                android:layout_height="40dp"
                android:layout_marginTop="16dp"
                style="@style/Widget.Material3.Button.TextButton"
                android:text="验证"
                android:textColor="#FFFFFF"
                android:gravity="center"
                android:layout_alignParentRight="true" 

                app:iconTint="#6200EE"/>

        </RelativeLayout>
    </LinearLayout>
</com.google.android.material.card.MaterialCardView>
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardBackgroundColor="#FFFFFF"
        app:cardCornerRadius="12dp"
        app:cardElevation="1dp"
        app:strokeColor="#DDDDDD"
        app:strokeWidth="1dp"
        android:layout_marginBottom="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="公告"
                    android:textSize="18sp"
                    android:textColor="#333333"
                    app:fontFamily="sans-serif-medium"/>

                <Space
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1"/>

                <com.google.android.material.imageview.ShapeableImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    app:srcCompat="@drawable/ic_info_outline"
                    app:tint="#333333"/>
            </LinearLayout>

            <com.google.android.material.textview.MaterialTextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="无界；让科技无界。"
                android:textSize="14sp"
                android:textColor="#333333"/>


                <RelativeLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="16dp">

    <com.google.android.material.button.MaterialButton
        android:id="@+id/notifyButton"
        android:layout_width="80dp"
        android:layout_height="40dp"
        android:layout_marginTop="16dp"
        style="@style/Widget.Material3.Button.TextButton"
        android:text="我要反馈"
        android:textColor="#FFFFFF"
        android:gravity="center"
        android:layout_alignParentRight="true" 
        app:iconTint="#6200EE"/>

</RelativeLayout>
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

</LinearLayout>

    </androidx.core.widget.NestedScrollView>

    </vertical>,  // 传入 XML 文件内容的字符串
    ui.viewPager,  // 父视图
    true  // 是否自动添加到父视图（可选，默认 false）
  );



ui.notifyButton.on("click",function(){
var contact;
threads.start(function() {
        try {
            // 在新线程中执行耗时操作
            contact = pjysdk.GetRemoteVar("QQ").result.value
            
            // 获取结果后通过回调返回给UI线程
            ui.run(() => {
添加qq(contact);
 
});

        } catch (e) {
            // 错误处理
            ui.run(() => {
                toastLog("网络获取发生异常！请联系客服反馈！");
            });
        }
    });
    //toastLog(pjysdk.GetRemoteVar("QQ").result.value);

});

    
threads.start(function() {
        try {
            // 在新线程中执行耗时操作
            login_ret = pjysdk.CardLogin();
            
            // 获取结果后通过回调返回给UI线程
            ui.run(() => {
                if (login_ret.code == 0) {
    storages.create("AuthorizationCode").put("AuthorizationCode",String(ui.AuthorizationCode.getText()));
    storages.create("expiresTime").put("expiresTime",pjysdk.login_result.expires);
    ui.start.enabled = true;
    //toastLog("验证成功！");
} else {
    // 登录失败提示
    log(login_ret.message);
    return;
}
});
        } catch (e) {
            // 错误处理
            ui.run(() => {
                toastLog("自动登录发生异常！请联系客服反馈！");
            });
        }
    });

//console.log("这行会立即执行，不会等待上面的延时");

ui.verifyButton.on("click",function(){
pjysdk.SetCard(String(ui.AuthorizationCode.getText()));

threads.start(function() {
        try {
            // 在新线程中执行耗时操作
            login_ret = pjysdk.CardLogin();
            
            // 获取结果后通过回调返回给UI线程
            ui.run(() => {
                if (login_ret.code == 0) {
    storages.create("AuthorizationCode").put("AuthorizationCode",String(ui.AuthorizationCode.getText()));
    storages.create("expiresTime").put("expiresTime",pjysdk.login_result.expires);
    ui.start.enabled = true;
    toastLog("验证成功！");
} else {
    // 登录失败提示
    toastLog(login_ret.message);
    return;
}
});
        } catch (e) {
            // 错误处理
            ui.run(() => {
                toastLog("验证登录发生异常！请联系客服反馈！");
            });
        }
    });
    
});


// 2. 读取 XML 文件内容为字符串
var myContent = files.read(myXmlPath);

// 3. 将字符串传入 $ui.inflate()，并指定父视图
var myUI = ui.inflate(myContent,
                ui.viewPager,  // 父视图
    true  // 是否自动添加到父视图（可选，默认 false）
    );

    /*
    function showMaterialAnnouncement(title, content, date, isImportant) {
        let window = floaty.rawWindow(
            <frame w="*" h="*" gravity="center" xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
                <com.google.android.material.card.MaterialCardView android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_margin="16dp" android:maxWidth="360dp" android:theme="@style/Theme.MaterialComponents.Light" app:cardBackgroundColor="#FFFFFF" app:cardCornerRadius="12dp" app:cardElevation="4dp" app:strokeColor="#DDDDDD" app:strokeWidth="1dp">
                    <LinearLayout android:layout_width="match_parent" android:layout_height="wrap_content" android:orientation="vertical" android:padding="16dp">
                        <LinearLayout android:layout_width="match_parent" android:layout_height="wrap_content" android:gravity="center_vertical" android:orientation="horizontal">
                            <com.google.android.material.textview.MaterialTextView android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="系统公告" android:textColor="#333333" android:textSize="18sp" app:fontFamily="sans-serif-medium"/>
                            <Space android:layout_width="0dp" android:layout_height="0dp" android:layout_weight="1"/>
                            <com.google.android.material.textview.MaterialTextView android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="2023-11-15" android:textColor="#999999" android:textSize="12sp"/>
                        </LinearLayout>
                        <com.google.android.material.textview.MaterialTextView android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_marginTop="12dp" android:lineSpacingExtra="4dp" android:text="亲爱的用户：\n\n我们即将在2023年11月20日凌晨2:00-4:00进行系统维护升级，届时服务将暂时不可用，请提前做好安排。\n\n感谢您的理解与支持！" android:textColor="#666666" android:textSize="14sp"/>
                        <com.google.android.material.chip.Chip android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="12dp" android:text="重要" android:textColor="#FF5252" android:visibility={isImportant ? "visible" : "gone"} app:chipBackgroundColor="#FFF2F2" app:chipStrokeColor="#FF5252" app:chipStrokeWidth="1dp" app:textEndPadding="8dp" app:textStartPadding="8dp"/>
                        <com.google.android.material.button.MaterialButton android:id="@+id/confirmBtn" android:layout_width="wrap_content" android:layout_height="40dp" android:layout_gravity="end" android:layout_marginTop="16dp" android:text="我知道了" android:textColor="#6200EE" style="@style/Widget.MaterialComponents.Button.OutlinedButton" app:strokeColor="#6200EE" app:strokeWidth="1dp"/>
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>
            </frame>
        );
        window.confirmBtn.on("click", () => window.close());
        ui.post(() => {
            const maxWidth = 360 * device.density;
            const cardWidth = Math.min(device.width - 64, maxWidth);
            const x = Math.round((device.width - cardWidth) / 2);
            const y = Math.round(device.height / 2);
            const safeX = isNaN(x) ? 16 : x;
            const safeY = isNaN(y) ? device.height / 3 : y;
            window.setPosition(safeX, safeY);
        }, 50);
        return window;
    }
    
    */

    
    function showAnnouncementDialog(title, content, date) {
        let dialog = ui.inflate(
            <frame bg="#00000000">
                <com.google.android.material.card.MaterialCardView w="*" h="auto" margin="20" cardBackgroundColor="#FFFFFF" cardCornerRadius="12" cardElevation="1" strokeColor="#DDDDDD" strokeWidth="1">
                    <vertical padding="16">
                        <horizontal gravity="center_vertical">
                            <text id="titleText" textSize="18" textColor="#333333" fontFamily="sans-serif-medium"/>
                        
                            <frame layout_weight="1"/>
                            <text id="dateText" textSize="12" textColor="#999999"/>
                        </horizontal>
                        
                        <com.google.android.material.textview.MaterialTextView id="contentText" textSize="14" textColor="#666666" marginTop="12" lineSpacingExtra="4"/>
                        
                        <com.google.android.material.button.MaterialButton id="confirmBtn" text="我知道了" style="Widget.Material3.Button.OutlinedButton" textColor="#6200EE" strokeColor="#6200EE" strokeWidth="1" marginTop="16" layout_gravity="right" w="auto" h="40"/>
                    </vertical>
                </com.google.android.material.card.MaterialCardView>
            </frame>
            /*
                    <frame w="*" h="*" gravity="center" bg="#00000000" xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
                    <com.google.android.material.card.MaterialCardView android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_margin="20dp" app:cardBackgroundColor="#FFFFFF" app:cardCornerRadius="12dp" app:cardElevation="1dp" app:strokeColor="#DDDDDD" app:strokeWidth="1dp">
                        <LinearLayout android:layout_width="match_parent" android:layout_height="wrap_content" android:orientation="vertical" android:padding="16dp">
                            <LinearLayout android:layout_width="match_parent" android:layout_height="wrap_content" android:gravity="center_vertical" android:orientation="horizontal">
                                <com.google.android.material.textview.MaterialTextView android:id="@+id/titleText" android:layout_width="wrap_content" android:layout_height="wrap_content" android:textSize="18sp" android:textColor="#333333" app:fontFamily="sans-serif-medium"/>
                                <Space android:layout_width="0dp" android:layout_height="0dp" android:layout_weight="1"/>
                                <com.google.android.material.textview.MaterialTextView android:id="@+id/dateText" android:layout_width="wrap_content" android:layout_height="wrap_content" android:textSize="12sp" android:textColor="#999999"/>
                            </LinearLayout>
                            <com.google.android.material.textview.MaterialTextView android:id="@+id/contentText" android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_marginTop="12dp" android:textSize="14sp" android:textColor="#666666" android:lineSpacingExtra="4dp"/>
                            <com.google.android.material.button.MaterialButton android:id="@+id/confirmBtn" android:layout_width="wrap_content" android:layout_height="40dp" android:layout_gravity="end" android:layout_marginTop="16dp" android:text="我知道了" android:textColor="#6200EE" style="@style/Widget.MaterialComponents.Button.OutlinedButton" app:strokeColor="#6200EE" app:strokeWidth="1dp"/>
                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>
                </frame>*/
        );
        
        dialogs.build({
    title: title,
    content: content,
    positive: "确定",
    negative: "取消"
}).show();


        
        dialog.confirmBtn.on("click", () => {
            dialog.dismiss();
        });
        
        return dialog;
    }
    

  
    
  
  var fn = function () {
    var r = ui.mySpinner.getSelectedItem()
    log(r)
  }
ui.updateLog.on("click",function(){
var updateLog;
    // 使用示例
    threads.start(function() {
        try {
            // 在新线程中执行耗时操作
             updateLog = pjysdk.GetRemoteVar("日志").result.value

            
            // 获取结果后通过回调返回给UI线程
            ui.run(() => {
                showAnnouncementDialog(
        "更新日志", 
        updateLog, 
        "2023-11-15"
    );
});
        } catch (e) {
            // 错误处理
            ui.run(() => {
                toastLog("获取更新日志异常！请联系客服反馈！");
            });
        }
    });
        


});
/**
 * 设置圆角矩形渐变背景
 * @param {View} view - 需要设置背景的视图对象
 * @param {string} startColor - 渐变起始颜色
 * @param {string} endColor - 渐变结束颜色
 * @param {number} cornerRadius - 圆角半径(单位：dp)
 */
 /**
 * 手动将 dp 转换为 px
 * @param {number} dp - 设备独立像素值
 * @returns {number} 像素值
 */

// 使用示例
function setBottomRoundedGradient(view, startColor, endColor, cornerRadiusDP) {
    if (!view) return;
    
    // 确保在主线程执行
    
        try {
            
            // 1. 转换单位
            var radiusPx = dip2px(cornerRadiusDP);
            
            // 2. 创建渐变
            var gradient = new android.graphics.drawable.GradientDrawable(
                android.graphics.drawable.GradientDrawable.Orientation.TOP_BOTTOM,
                [colors.parseColor(startColor), colors.parseColor(endColor)]
            );
            
            // 3. 设置仅底部圆角
            gradient.setCornerRadii([
                0, 0,        // 左上
                0, 0,        // 右上
                radiusPx, radiusPx,  // 右下
                radiusPx, radiusPx   // 左下
            ]);
            
            // 4. 应用背景
            view.setBackground(gradient);
            //toastLog("圆角半径设置:"+radiusPx + "px");
            // 5. 调试日志
            
        } catch(e) {
            toastLog("设置背景失败:", e);
        }
    
}


setBottomRoundedGradient(myUI.page3Bg, "#373BD3", "#00373BD3", 50);
//轮播等待时间
var times = 0;
//定时器
var id;
//页面更改侦听器
ui.viewpager_img.setOnPageChangeListener({
    //已选定页面发生改变时触发
    onPageSelected: function (index) {
        //如果当前页面为第0个页面 (0就是1)
        if (index == 0) {
            index = ui.viewpager_img.childCount - 2
            setTimeout(function () {
                //关闭跳转动画并跳转到倒数第二个页面 false表示关闭动画
                ui.viewpager_img.setCurrentItem(index, false)
            }, 300)
            //如果当前页面为最后一个页面 
        } else if (index == ui.viewpager_img.childCount - 1) {
            index = 1
            setTimeout(function () {
                //关闭跳转动画并跳转到第1个页面(前面还有一个第0个页面) false表示关闭动画
                ui.viewpager_img.setCurrentItem(index, false)
            }, 300)
        }
        //更改小白球显示
        tabs_view[tabs_index].setTextColor(colors.parseColor("#90000000"))
        tabs_index = index - 1
        tabs_view[tabs_index].setTextColor(colors.parseColor("#ffffff"))
        times = 0
    }
})

  //设定界面为编号1
ui.viewpager_img.currentItem = 1;

var waitTime = setInterval(function () {
    (times + 1) * 1000 >= data.time ? (ui.viewpager_img.currentItem = ui.viewpager_img.currentItem + 1, times = 0) : times++
}, 1000);

ui.mySpinner.setOnItemSelectedListener(myAdapterListener)
var view = ui.mySpinner
    view.setSelection(0, false)
//log(view.setAdapter.toString())
/* 修改spinner选项
var mFavorite = ["财富","权利","事业","家庭","健康"]
setTimeout(
  function () {
    change_list(ui.mySpinner, mFavorite)
  }, 10000
)

function change_list(spinner, mFavorite) {
  sp = spinner
  adapter = new android.widget.ArrayAdapter(context, android.R.layout.simple_spinner_item, mFavorite);
  adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
  sp.setAdapter(adapter);
}*/



/*
//open点击事件
ui.open.click(() => {
    if (ui.open.text() == "停止轮播") {
        clearInterval(id);
        ui.open.setText("开启轮播")
        toast("已停止图片轮播")
        return
    }
    data.time = parseInt(ui.time.text())
    if (!data.time > 0) {
        toast("时间错误请检查轮播时间");
        return
    }
    ui.open.setText("停止轮播")
    toast("已开启图片轮播")
    id = setInterval(function () {
        (time + 1) * 1000 >= data.time ? (ui.viewpager_img.currentItem = ui.viewpager_img.currentItem + 1, time = 0) : time++
    }, 1000);
})
*/

// 设置ViewPager的页面为他的子View
ui.viewPager.initAdapterFromChildren();

// 底部三个按钮的id
var navigationIds = [
    ui.R.id.navigation_home,
    ui.R.id.navigation_dashboard,
    ui.R.id.navigation_notifications
];

// 当底部按钮被选中时，切换ViewPager页面为相应位置的页面
ui.navigation.setOnNavigationItemSelectedListener(function(item) {
    ui.viewPager.currentItem = navigationIds.indexOf(item.itemId);
    
    return true;
});

// 当ViewPager页面切换时，切换底部按钮的状态
ui.viewPager.addOnPageChangeListener(new Packages.androidx.viewpager.widget.ViewPager.OnPageChangeListener({
    onPageSelected: function(position) {
        ui.navigation.setSelectedItemId(navigationIds[position]);
        if(position==1 || position==2){
            ui.toolbar.setVisibility(android.view.View.GONE);
        } else {
            ui.toolbar.setVisibility(android.view.View.VISIBLE);
        }
    }
}));
}



    {
        //无界
        //配置操作
    {
    function time() {
        var year = new Date().getFullYear();
        if (new Date().getMonth() + 1 < 10) {
            var month = "0" + (new Date().getMonth() + 1);
        } else {
            var month = new Date().getMonth() + 1;
        }
        if (new Date().getDate() < 10) {
            var date = "0" + new Date().getDate();
        } else {
            var date = new Date().getDate();
        }
        if (new Date().getHours() < 10) {
            var time = "0" + new Date().getHours();
        } else {
            var time = new Date().getHours();
        }
        return year + "" + month + "" + date
    };
        ui.autoService.on("check", function (checked) {
            // 用户勾选无障碍服务的选项时，跳转到页面让用户去开启
            if (checked && auto.service == null) {
                app.startActivity({
                    action: "android.settings.ACCESSIBILITY_SETTINGS"
                });
            }
            if (!checked && auto.service != null) {
                auto.service.disableSelf();
            }
        });
        ui.floatingWindow.on("check", function (checked) {
            //申请悬浮窗
            importClass(android.content.Intent);
            importClass(android.net.Uri);
            importClass(android.provider.Settings);
            var intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                Uri.parse("package:" + context.getPackageName()));
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            app.startActivity(intent);
        });
        ui.emitter.on("resume", function () {
            ui.autoService.checked = auto.service != null;
            ui.floatingWindow.checked = floaty.checkPermission() != false
        });
     
        //单选正版
        ui.genuine.on("click", function () {
            ui.volcano.setChecked(false);
            ui.selected.setChecked(false);
            ui.shoppingMall.setChecked(false);
            ui.search.setChecked(false);
            ui.qishui.setChecked(false);
            ui.duoshan.setChecked(false);
            ui.gangban.setChecked(false);
        });
        //单选火山
        ui.volcano.on("click", function () {
            ui.genuine.setChecked(false);
            ui.selected.setChecked(false);
            ui.shoppingMall.setChecked(false);
            ui.search.setChecked(false);
            ui.qishui.setChecked(false);
            ui.duoshan.setChecked(false);
            ui.gangban.setChecked(false);
        });
        //单选精选
        ui.selected.on("click", function () {
            ui.volcano.setChecked(false);
            ui.genuine.setChecked(false);
            ui.shoppingMall.setChecked(false);
            ui.search.setChecked(false);
            ui.qishui.setChecked(false);
            ui.duoshan.setChecked(false);
            ui.gangban.setChecked(false);
        });
        //单选商城
        ui.shoppingMall.on("click", function () {
            ui.volcano.setChecked(false);
            ui.selected.setChecked(false);
            ui.genuine.setChecked(false);
            ui.search.setChecked(false);
            ui.qishui.setChecked(false);
            ui.duoshan.setChecked(false);
            ui.gangban.setChecked(false);
        });
        //单选搜索
        ui.search.on("click", function () {
            ui.volcano.setChecked(false);
            ui.selected.setChecked(false);
            ui.shoppingMall.setChecked(false);
            ui.genuine.setChecked(false);
            ui.qishui.setChecked(false);
            ui.duoshan.setChecked(false);
            ui.gangban.setChecked(false);
        });
        //单选汽水
        ui.qishui.on("click", function () {
            ui.volcano.setChecked(false);
            ui.selected.setChecked(false);
            ui.shoppingMall.setChecked(false);
            ui.genuine.setChecked(false);
            ui.search.setChecked(false);
            ui.duoshan.setChecked(false);
            ui.gangban.setChecked(false);
        });
        //单选多闪
        ui.duoshan.on("click", function () {
            ui.volcano.setChecked(false);
            ui.selected.setChecked(false);
            ui.shoppingMall.setChecked(false);
            ui.genuine.setChecked(false);
            ui.search.setChecked(false);
            ui.qishui.setChecked(false);
            ui.gangban.setChecked(false);
        });
        //单选港版
        ui.gangban.on("click", function () {
            ui.volcano.setChecked(false);
            ui.selected.setChecked(false);
            ui.shoppingMall.setChecked(false);
            ui.genuine.setChecked(false);
            ui.search.setChecked(false);
            ui.qishui.setChecked(false);
            ui.duoshan.setChecked(false);
        });
        //单选检测
        ui.jiance.on("click", function () {
            ui.xihuan.setChecked(false);
        });
        //单选喜欢
        ui.xihuan.on("click", function () {
            ui.jiance.setChecked(false);
        });
        //单选随机
        ui.zanyan.on("click", function () {
            ui.xinyouxu.setChecked(false);
            ui.huoli.setChecked(false);
            ui.shunxu.setChecked(false);
        });
        //单选有序
        ui.xinyouxu.on("click", function () {
            ui.zanyan.setChecked(false);
            ui.huoli.setChecked(false);
            ui.shunxu.setChecked(false);
        });
        //单选火力
        ui.huoli.on("click", function () {
            ui.xinyouxu.setChecked(false);
            ui.zanyan.setChecked(false);
            ui.shunxu.setChecked(false);
        });
        //单选顺序
        ui.shunxu.on("click", function () {
            ui.xinyouxu.setChecked(false);
            ui.huoli.setChecked(false);
            ui.zanyan.setChecked(false);
        });
        //常规配置
        ui.conventionConfig.on("click", function () {
            {
                //常规清空
                {
                    storages.create(time() + "arr").remove(time() + "arr");
                    storages.create(time() + "allCount").remove(time() + "allCount");
                    storages.create(time() + "allTaskLength_convention").remove(time() + "allTaskLength_convention");
                    storages.create(time() + "likeLength_convention").remove(time() + "likeLength_convention");
                    storages.create(time() + "imageTextLength_convention").remove(time() + "imageTextLength_convention");
                    storages.create(time() + "profilePictureLength_convention").remove(time() + "profilePictureLength_convention");
                    storages.create(time() + "collectLength_convention").remove(time() + "collectLength_convention");
                    storages.create(time() + "commentLength_convention").remove(time() + "commentLength_convention");
                    storages.create(time() + "followWithInterestLength_convention").remove(time() + "followWithInterestLength_convention");
                    storages.create(time() + "privateLetterLength_convention").remove(time() + "privateLetterLength_convention");
                    storages.create(time() + "shareLength_convention").remove(time() + "shareLength_convention");
                    storages.create(time() + "clickCommentLength_convention").remove(time() + "clickCommentLength_convention");
                    storages.create(time() + "recordLikeCount_convention").remove(time() + "recordLikeCount_convention");
                    storages.create(time() + "recordImageTextCount_convention").remove(time() + "recordImageTextCount_convention");
                    storages.create(time() + "recordProfilePictureCount_convention").remove(time() + "recordProfilePictureCount_convention");
                    storages.create(time() + "recordCollectCount_convention").remove(time() + "recordCollectCount_convention");
                    storages.create(time() + "recordCommentCount_convention").remove(time() + "recordCommentCount_convention");
                    storages.create(time() + "recordFollowWithInterestCount_convention").remove(time() + "recordFollowWithInterestCount_convention");
                    storages.create(time() + "recordPrivateLetterCount_convention").remove(time() + "recordPrivateLetterCount_convention");
                    storages.create(time() + "recordShareCount_convention").remove(time() + "recordShareCount_convention");
                    storages.create(time() + "recordClickCommentCount_convention").remove(time() + "recordClickCommentCount_convention");
                    storages.create("imageTextNextTimes").remove("imageTextNextTimes");
                    storages.create("recordLikeTime").remove("recordLikeTime");
                }
    
                //功能
                ui.convention.setChecked(true);
                ui.video.setChecked(false);
                //任务
                //点赞
                ui.like.setText("5000");
                ui.liketl.setText("0");
                ui.switchLike.setChecked(true);
                //图文
                ui.imageText.setText("5000");
                ui.imageTexttl.setText("0");
                ui.switchImageText.setChecked(true);
                //头像
                ui.profilePicture.setText("0");
                ui.profilePicturetl.setText("0");
                ui.switchProfilePicture.setChecked(false);
                //收藏
                ui.collect.setText("400");
                ui.collecttl.setText("0");
                ui.switchCollect.setChecked(true);
                //评论
                ui.comment.setText("400");
                ui.commenttl.setText("0");
                ui.switchComment.setChecked(true);
                //关注
                ui.followWithInterest.setText("0");
                ui.followWithInteresttl.setText("0");
                ui.switchFollowWithInterest.setChecked(false);
                //私信
                ui.privateLetter.setText("0");
                ui.privateLettertl.setText("0");
                ui.switchPrivateLetter.setChecked(false);
                //分享
                ui.share.setText("200");
                ui.sharetl.setText("0");
                ui.switchShare.setChecked(true);
                //点评
                ui.clickComment.setText("0");
                ui.clickCommenttl.setText("0");
                ui.switchClickComment.setChecked(false);
                //返回停留
                ui.backSpeed.setText("3");
                //重启
                ui.restartCount.setText("300");
                //每运行多少分钟休息多少分钟
                ui.meiyunxing.setText("0");
                ui.xiuxi.setText("0");
                //连续多少次点赞失败将休息多少分钟
                ui.lianxu.setText("2");
                ui.jiangxiuxi.setText("60");
                //每多少秒做一次任务
                ui.duoshaomiao.setText("0");
                //版本
                ui.genuine.setChecked(false);
                ui.volcano.setChecked(false);
                ui.selected.setChecked(false);
                ui.shoppingMall.setChecked(false);
                ui.search.setChecked(false);
                ui.qishui.setChecked(false);
                ui.duoshan.setChecked(true);
                ui.gangban.setChecked(false);
                //其他
                ui.selfSearching.setChecked(true);
                ui.delay.setChecked(true);
                ui.rest.setChecked(false);
                ui.zanyan.setChecked(false);
                ui.baiping.setChecked(false);
                ui.jiance.setChecked(true);
                ui.xihuan.setChecked(false);
                ui.liuhen.setChecked(false);
                ui.huoli.setChecked(false);
                ui.xinyouxu.setChecked(false);
                ui.shunxu.setChecked(false);
                ui.xinhun.setChecked(false);
            }
        });
        //火力配置
        ui.huoliConfig.on("click", function () {
            {
                //常规清空
                {
                    storages.create(time() + "arr").remove(time() + "arr");
                    storages.create(time() + "allCount").remove(time() + "allCount");
                    storages.create(time() + "allTaskLength_convention").remove(time() + "allTaskLength_convention");
                    storages.create(time() + "likeLength_convention").remove(time() + "likeLength_convention");
                    storages.create(time() + "imageTextLength_convention").remove(time() + "imageTextLength_convention");
                    storages.create(time() + "profilePictureLength_convention").remove(time() + "profilePictureLength_convention");
                    storages.create(time() + "collectLength_convention").remove(time() + "collectLength_convention");
                    storages.create(time() + "commentLength_convention").remove(time() + "commentLength_convention");
                    storages.create(time() + "followWithInterestLength_convention").remove(time() + "followWithInterestLength_convention");
                    storages.create(time() + "privateLetterLength_convention").remove(time() + "privateLetterLength_convention");
                    storages.create(time() + "shareLength_convention").remove(time() + "shareLength_convention");
                    storages.create(time() + "clickCommentLength_convention").remove(time() + "clickCommentLength_convention");
                    storages.create(time() + "recordLikeCount_convention").remove(time() + "recordLikeCount_convention");
                    storages.create(time() + "recordImageTextCount_convention").remove(time() + "recordImageTextCount_convention");
                    storages.create(time() + "recordProfilePictureCount_convention").remove(time() + "recordProfilePictureCount_convention");
                    storages.create(time() + "recordCollectCount_convention").remove(time() + "recordCollectCount_convention");
                    storages.create(time() + "recordCommentCount_convention").remove(time() + "recordCommentCount_convention");
                    storages.create(time() + "recordFollowWithInterestCount_convention").remove(time() + "recordFollowWithInterestCount_convention");
                    storages.create(time() + "recordPrivateLetterCount_convention").remove(time() + "recordPrivateLetterCount_convention");
                    storages.create(time() + "recordShareCount_convention").remove(time() + "recordShareCount_convention");
                    storages.create(time() + "recordClickCommentCount_convention").remove(time() + "recordClickCommentCount_convention");
                    storages.create("imageTextNextTimes").remove("imageTextNextTimes");
                    storages.create("recordLikeTime").remove("recordLikeTime");
                }
    
                //功能
                ui.convention.setChecked(true);
                ui.video.setChecked(false);
                //任务
                //点赞
                ui.like.setText("2000");
                ui.liketl.setText("0");
                ui.switchLike.setChecked(true);
                //图文
                ui.imageText.setText("1000");
                ui.imageTexttl.setText("0");
                ui.switchImageText.setChecked(true);
                //头像
                ui.profilePicture.setText("0");
                ui.profilePicturetl.setText("0");
                ui.switchProfilePicture.setChecked(false);
                //收藏
                ui.collect.setText("400");
                ui.collecttl.setText("0");
                ui.switchCollect.setChecked(true);
                //评论
                ui.comment.setText("400");
                ui.commenttl.setText("0");
                ui.switchComment.setChecked(true);
                //关注
                ui.followWithInterest.setText("0");
                ui.followWithInteresttl.setText("0");
                ui.switchFollowWithInterest.setChecked(false);
                //私信
                ui.privateLetter.setText("0");
                ui.privateLettertl.setText("0");
                ui.switchPrivateLetter.setChecked(false);
                //分享
                ui.share.setText("200");
                ui.sharetl.setText("0");
                ui.switchShare.setChecked(true);
                //点评
                ui.clickComment.setText("0");
                ui.clickCommenttl.setText("0");
                ui.switchClickComment.setChecked(false);
                //返回停留
                ui.backSpeed.setText("3");
                //重启
                ui.restartCount.setText("300");
                //每运行多少分钟休息多少分钟
                ui.meiyunxing.setText("0");
                ui.xiuxi.setText("0");
                //连续多少次点赞失败将休息多少分钟
                ui.lianxu.setText("0");
                ui.jiangxiuxi.setText("0");
                //每多少秒做一次任务
                ui.duoshaomiao.setText("0");
                //版本
                ui.genuine.setChecked(false);
                ui.volcano.setChecked(false);
                ui.selected.setChecked(false);
                ui.shoppingMall.setChecked(false);
                ui.search.setChecked(false);
                ui.qishui.setChecked(false);
                ui.duoshan.setChecked(true);
                ui.gangban.setChecked(false);
                //其他
                ui.selfSearching.setChecked(true);
                ui.delay.setChecked(true);
                ui.rest.setChecked(false);
                ui.zanyan.setChecked(false);
                ui.baiping.setChecked(false);
                ui.jiance.setChecked(false);
                ui.xihuan.setChecked(false);
                ui.liuhen.setChecked(false);
                ui.huoli.setChecked(true);
                ui.xinyouxu.setChecked(false);
                ui.shunxu.setChecked(false);
                ui.xinhun.setChecked(false);
            }
        });
        //有序配置
        ui.youxuConfig.on("click", function () {
            {
                //常规清空
                {
                    storages.create(time() + "arr").remove(time() + "arr");
                    storages.create(time() + "allCount").remove(time() + "allCount");
                    storages.create(time() + "allTaskLength_convention").remove(time() + "allTaskLength_convention");
                    storages.create(time() + "likeLength_convention").remove(time() + "likeLength_convention");
                    storages.create(time() + "imageTextLength_convention").remove(time() + "imageTextLength_convention");
                    storages.create(time() + "profilePictureLength_convention").remove(time() + "profilePictureLength_convention");
                    storages.create(time() + "collectLength_convention").remove(time() + "collectLength_convention");
                    storages.create(time() + "commentLength_convention").remove(time() + "commentLength_convention");
                    storages.create(time() + "followWithInterestLength_convention").remove(time() + "followWithInterestLength_convention");
                    storages.create(time() + "privateLetterLength_convention").remove(time() + "privateLetterLength_convention");
                    storages.create(time() + "shareLength_convention").remove(time() + "shareLength_convention");
                    storages.create(time() + "clickCommentLength_convention").remove(time() + "clickCommentLength_convention");
                    storages.create(time() + "recordLikeCount_convention").remove(time() + "recordLikeCount_convention");
                    storages.create(time() + "recordImageTextCount_convention").remove(time() + "recordImageTextCount_convention");
                    storages.create(time() + "recordProfilePictureCount_convention").remove(time() + "recordProfilePictureCount_convention");
                    storages.create(time() + "recordCollectCount_convention").remove(time() + "recordCollectCount_convention");
                    storages.create(time() + "recordCommentCount_convention").remove(time() + "recordCommentCount_convention");
                    storages.create(time() + "recordFollowWithInterestCount_convention").remove(time() + "recordFollowWithInterestCount_convention");
                    storages.create(time() + "recordPrivateLetterCount_convention").remove(time() + "recordPrivateLetterCount_convention");
                    storages.create(time() + "recordShareCount_convention").remove(time() + "recordShareCount_convention");
                    storages.create(time() + "recordClickCommentCount_convention").remove(time() + "recordClickCommentCount_convention");
                    storages.create("imageTextNextTimes").remove("imageTextNextTimes");
                    storages.create("recordLikeTime").remove("recordLikeTime");
                }
    
                //功能
                ui.convention.setChecked(true);
                ui.video.setChecked(false);
                //任务
                //点赞
                ui.like.setText("0");
                ui.liketl.setText("0");
                ui.switchLike.setChecked(false);
                //图文
                ui.imageText.setText("0");
                ui.imageTexttl.setText("0");
                ui.switchImageText.setChecked(false);
                //头像
                ui.profilePicture.setText("0");
                ui.profilePicturetl.setText("0");
                ui.switchProfilePicture.setChecked(false);
                //收藏
                ui.collect.setText("400");
                ui.collecttl.setText("0");
                ui.switchCollect.setChecked(true);
                //评论
                ui.comment.setText("400");
                ui.commenttl.setText("0");
                ui.switchComment.setChecked(true);
                //关注
                ui.followWithInterest.setText("0");
                ui.followWithInteresttl.setText("0");
                ui.switchFollowWithInterest.setChecked(false);
                //私信
                ui.privateLetter.setText("0");
                ui.privateLettertl.setText("0");
                ui.switchPrivateLetter.setChecked(false);
                //分享
                ui.share.setText("200");
                ui.sharetl.setText("0");
                ui.switchShare.setChecked(true);
                //点评
                ui.clickComment.setText("1000");
                ui.clickCommenttl.setText("0");
                ui.switchClickComment.setChecked(true);
                //返回停留
                ui.backSpeed.setText("3");
                //重启
                ui.restartCount.setText("300");
                //每运行多少分钟休息多少分钟
                ui.meiyunxing.setText("0");
                ui.xiuxi.setText("0");
                //连续多少次点赞失败将休息多少分钟
                ui.lianxu.setText("0");
                ui.jiangxiuxi.setText("0");
                //每多少秒做一次任务
                ui.duoshaomiao.setText("0");
                //版本
                ui.genuine.setChecked(false);
                ui.volcano.setChecked(false);
                ui.selected.setChecked(false);
                ui.shoppingMall.setChecked(false);
                ui.search.setChecked(false);
                ui.qishui.setChecked(false);
                ui.duoshan.setChecked(true);
                ui.gangban.setChecked(false);
                //其他
                ui.selfSearching.setChecked(true);
                ui.delay.setChecked(true);
                ui.rest.setChecked(false);
                ui.zanyan.setChecked(false);
                ui.baiping.setChecked(false);
                ui.jiance.setChecked(false);
                ui.xihuan.setChecked(false);
                ui.liuhen.setChecked(false);
                ui.huoli.setChecked(false);
                ui.xinyouxu.setChecked(true);
                ui.shunxu.setChecked(false);
                ui.xinhun.setChecked(false);
            }
        });
        //汽水配置
        ui.qishuiyinyueConfig.on("click", function () {
            {
                //常规清空
                {
                    storages.create(time() + "arr").remove(time() + "arr");
                    storages.create(time() + "allCount").remove(time() + "allCount");
                    storages.create(time() + "allTaskLength_convention").remove(time() + "allTaskLength_convention");
                    storages.create(time() + "likeLength_convention").remove(time() + "likeLength_convention");
                    storages.create(time() + "imageTextLength_convention").remove(time() + "imageTextLength_convention");
                    storages.create(time() + "profilePictureLength_convention").remove(time() + "profilePictureLength_convention");
                    storages.create(time() + "collectLength_convention").remove(time() + "collectLength_convention");
                    storages.create(time() + "commentLength_convention").remove(time() + "commentLength_convention");
                    storages.create(time() + "followWithInterestLength_convention").remove(time() + "followWithInterestLength_convention");
                    storages.create(time() + "privateLetterLength_convention").remove(time() + "privateLetterLength_convention");
                    storages.create(time() + "shareLength_convention").remove(time() + "shareLength_convention");
                    storages.create(time() + "clickCommentLength_convention").remove(time() + "clickCommentLength_convention");
                    storages.create(time() + "recordLikeCount_convention").remove(time() + "recordLikeCount_convention");
                    storages.create(time() + "recordImageTextCount_convention").remove(time() + "recordImageTextCount_convention");
                    storages.create(time() + "recordProfilePictureCount_convention").remove(time() + "recordProfilePictureCount_convention");
                    storages.create(time() + "recordCollectCount_convention").remove(time() + "recordCollectCount_convention");
                    storages.create(time() + "recordCommentCount_convention").remove(time() + "recordCommentCount_convention");
                    storages.create(time() + "recordFollowWithInterestCount_convention").remove(time() + "recordFollowWithInterestCount_convention");
                    storages.create(time() + "recordPrivateLetterCount_convention").remove(time() + "recordPrivateLetterCount_convention");
                    storages.create(time() + "recordShareCount_convention").remove(time() + "recordShareCount_convention");
                    storages.create(time() + "recordClickCommentCount_convention").remove(time() + "recordClickCommentCount_convention");
                    storages.create("imageTextNextTimes").remove("imageTextNextTimes");
                    storages.create("recordLikeTime").remove("recordLikeTime");
                }
    
                //功能
                ui.convention.setChecked(true);
                ui.video.setChecked(false);
                //任务
                //点赞
                ui.like.setText("3000");
                ui.liketl.setText("0");
                ui.switchLike.setChecked(true);
                //图文
                ui.imageText.setText("0");
                ui.imageTexttl.setText("0");
                ui.switchImageText.setChecked(false);
                //头像
                ui.profilePicture.setText("0");
                ui.profilePicturetl.setText("0");
                ui.switchProfilePicture.setChecked(false);
                //收藏
                ui.collect.setText("0");
                ui.collecttl.setText("0");
                ui.switchCollect.setChecked(false);
                //评论
                ui.comment.setText("0");
                ui.commenttl.setText("0");
                ui.switchComment.setChecked(false);
                //关注
                ui.followWithInterest.setText("0");
                ui.followWithInteresttl.setText("0");
                ui.switchFollowWithInterest.setChecked(false);
                //私信
                ui.privateLetter.setText("0");
                ui.privateLettertl.setText("0");
                ui.switchPrivateLetter.setChecked(false);
                //分享
                ui.share.setText("0");
                ui.sharetl.setText("0");
                ui.switchShare.setChecked(false);
                //点评
                ui.clickComment.setText("0");
                ui.clickCommenttl.setText("0");
                ui.switchClickComment.setChecked(false);
                //返回停留
                ui.backSpeed.setText("3");
                //重启
                ui.restartCount.setText("300");
                //每运行多少分钟休息多少分钟
                ui.meiyunxing.setText("0");
                ui.xiuxi.setText("0");
                //连续多少次点赞失败将休息多少分钟
                ui.lianxu.setText("0");
                ui.jiangxiuxi.setText("0");
                //每多少秒做一次任务
                ui.duoshaomiao.setText("0");
                //版本
                ui.genuine.setChecked(false);
                ui.volcano.setChecked(false);
                ui.selected.setChecked(false);
                ui.shoppingMall.setChecked(false);
                ui.search.setChecked(false);
                ui.qishui.setChecked(true);
                ui.duoshan.setChecked(false);
                ui.gangban.setChecked(false);
                //其他
                ui.selfSearching.setChecked(false);
                ui.delay.setChecked(true);
                ui.rest.setChecked(false);
                ui.zanyan.setChecked(false);
                ui.baiping.setChecked(false);
                ui.jiance.setChecked(false);
                ui.xihuan.setChecked(false);
                ui.liuhen.setChecked(false);
                ui.huoli.setChecked(false);
                ui.xinyouxu.setChecked(false);
                ui.shunxu.setChecked(false);
                ui.xinhun.setChecked(false);
            }
        });
        //喜欢配置
        ui.xihuanConfig.on("click", function () {
            //常规清空
    
    storages.create(time() + "arr").remove(time() + "arr");
    storages.create(time() + "allCount").remove(time() + "allCount");
    storages.create(time() + "allTaskLength_convention").remove(time() + "allTaskLength_convention");
    storages.create(time() + "likeLength_convention").remove(time() + "likeLength_convention");
    storages.create(time() + "imageTextLength_convention").remove(time() + "imageTextLength_convention");
    storages.create(time() + "profilePictureLength_convention").remove(time() + "profilePictureLength_convention");
    storages.create(time() + "collectLength_convention").remove(time() + "collectLength_convention");
    storages.create(time() + "commentLength_convention").remove(time() + "commentLength_convention");
    storages.create(time() + "followWithInterestLength_convention").remove(time() + "followWithInterestLength_convention");
    storages.create(time() + "privateLetterLength_convention").remove(time() + "privateLetterLength_convention");
    storages.create(time() + "shareLength_convention").remove(time() + "shareLength_convention");
    storages.create(time() + "clickCommentLength_convention").remove(time() + "clickCommentLength_convention");
    storages.create(time() + "recordLikeCount_convention").remove(time() + "recordLikeCount_convention");
    storages.create(time() + "recordImageTextCount_convention").remove(time() + "recordImageTextCount_convention");
    storages.create(time() + "recordProfilePictureCount_convention").remove(time() + "recordProfilePictureCount_convention");
    storages.create(time() + "recordCollectCount_convention").remove(time() + "recordCollectCount_convention");
    storages.create(time() + "recordCommentCount_convention").remove(time() + "recordCommentCount_convention");
    storages.create(time() + "recordFollowWithInterestCount_convention").remove(time() + "recordFollowWithInterestCount_convention");
    storages.create(time() + "recordPrivateLetterCount_convention").remove(time() + "recordPrivateLetterCount_convention");
    storages.create(time() + "recordShareCount_convention").remove(time() + "recordShareCount_convention");
    storages.create(time() + "recordClickCommentCount_convention").remove(time() + "recordClickCommentCount_convention");
    storages.create("imageTextNextTimes").remove("imageTextNextTimes");
    storages.create("recordLikeTime").remove("recordLikeTime");
    
    
    //功能
    ui.convention.setChecked(true);
    ui.video.setChecked(false);
    //任务
    //点赞
    ui.like.setText("400");
    ui.liketl.setText("0");
    ui.switchLike.setChecked(true);
    //图文
    ui.imageText.setText("0");
    ui.imageTexttl.setText("0");
    ui.switchImageText.setChecked(false);
    //头像
    ui.profilePicture.setText("0");
    ui.profilePicturetl.setText("0");
    ui.switchProfilePicture.setChecked(false);
    //收藏
    ui.collect.setText("200");
    ui.collecttl.setText("0");
    ui.switchCollect.setChecked(true);
    //评论
    ui.comment.setText("200");
    ui.commenttl.setText("0");
    ui.switchComment.setChecked(true);
    //关注
    ui.followWithInterest.setText("0");
    ui.followWithInteresttl.setText("0");
    ui.switchFollowWithInterest.setChecked(false);
    //私信
    ui.privateLetter.setText("0");
    ui.privateLettertl.setText("0");
    ui.switchPrivateLetter.setChecked(false);
    //分享
    ui.share.setText("200");
    ui.sharetl.setText("0");
    ui.switchShare.setChecked(true);
    //点评
    ui.clickComment.setText("200");
    ui.clickCommenttl.setText("0");
    ui.switchClickComment.setChecked(true);
    //返回停留
    ui.backSpeed.setText("3");
    //重启
    ui.restartCount.setText("300");
    //每运行多少分钟休息多少分钟
    ui.meiyunxing.setText("0");
    ui.xiuxi.setText("0");
    //连续多少次点赞失败将休息多少分钟
    ui.lianxu.setText("0");
    ui.jiangxiuxi.setText("0");
    //每多少秒做一次任务
    ui.duoshaomiao.setText("60");
    //版本
    ui.genuine.setChecked(false);
    ui.volcano.setChecked(false);
    ui.selected.setChecked(false);
    ui.shoppingMall.setChecked(false);
    ui.search.setChecked(true);
    ui.qishui.setChecked(false);
    ui.duoshan.setChecked(false);
    ui.gangban.setChecked(false);
    //其他
    ui.selfSearching.setChecked(true);
    ui.delay.setChecked(true);
    ui.rest.setChecked(false);
    ui.zanyan.setChecked(false);
    ui.baiping.setChecked(false);
    ui.jiance.setChecked(false);
    ui.xihuan.setChecked(false);
    ui.liuhen.setChecked(false);
    ui.huoli.setChecked(false);
    ui.xinyouxu.setChecked(false);
    ui.shunxu.setChecked(false);
    ui.xinhun.setChecked(true);
        });
        //港版配置
        ui.imageTextConfig.on("click", function () {
            {
                //常规清空
                {
                    storages.create(time() + "arr").remove(time() + "arr");
                    storages.create(time() + "allCount").remove(time() + "allCount");
                    storages.create(time() + "allTaskLength_convention").remove(time() + "allTaskLength_convention");
                    storages.create(time() + "likeLength_convention").remove(time() + "likeLength_convention");
                    storages.create(time() + "imageTextLength_convention").remove(time() + "imageTextLength_convention");
                    storages.create(time() + "profilePictureLength_convention").remove(time() + "profilePictureLength_convention");
                    storages.create(time() + "collectLength_convention").remove(time() + "collectLength_convention");
                    storages.create(time() + "commentLength_convention").remove(time() + "commentLength_convention");
                    storages.create(time() + "followWithInterestLength_convention").remove(time() + "followWithInterestLength_convention");
                    storages.create(time() + "privateLetterLength_convention").remove(time() + "privateLetterLength_convention");
                    storages.create(time() + "shareLength_convention").remove(time() + "shareLength_convention");
                    storages.create(time() + "clickCommentLength_convention").remove(time() + "clickCommentLength_convention");
                    storages.create(time() + "recordLikeCount_convention").remove(time() + "recordLikeCount_convention");
                    storages.create(time() + "recordImageTextCount_convention").remove(time() + "recordImageTextCount_convention");
                    storages.create(time() + "recordProfilePictureCount_convention").remove(time() + "recordProfilePictureCount_convention");
                    storages.create(time() + "recordCollectCount_convention").remove(time() + "recordCollectCount_convention");
                    storages.create(time() + "recordCommentCount_convention").remove(time() + "recordCommentCount_convention");
                    storages.create(time() + "recordFollowWithInterestCount_convention").remove(time() + "recordFollowWithInterestCount_convention");
                    storages.create(time() + "recordPrivateLetterCount_convention").remove(time() + "recordPrivateLetterCount_convention");
                    storages.create(time() + "recordShareCount_convention").remove(time() + "recordShareCount_convention");
                    storages.create(time() + "recordClickCommentCount_convention").remove(time() + "recordClickCommentCount_convention");
                    storages.create("imageTextNextTimes").remove("imageTextNextTimes");
                    storages.create("recordLikeTime").remove("recordLikeTime");
                }
    
                //功能
                ui.convention.setChecked(true);
                ui.video.setChecked(false);
                //任务
                //点赞
                ui.like.setText("2000");
                ui.liketl.setText("0");
                ui.switchLike.setChecked(true);
                //图文
                ui.imageText.setText("1000");
                ui.imageTexttl.setText("0");
                ui.switchImageText.setChecked(true);
                //头像
                ui.profilePicture.setText("0");
                ui.profilePicturetl.setText("0");
                ui.switchProfilePicture.setChecked(false);
                //收藏
                ui.collect.setText("400");
                ui.collecttl.setText("0");
                ui.switchCollect.setChecked(true);
                //评论
                ui.comment.setText("400");
                ui.commenttl.setText("0");
                ui.switchComment.setChecked(true);
                //关注
                ui.followWithInterest.setText("0");
                ui.followWithInteresttl.setText("0");
                ui.switchFollowWithInterest.setChecked(false);
                //私信
                ui.privateLetter.setText("0");
                ui.privateLettertl.setText("0");
                ui.switchPrivateLetter.setChecked(false);
                //分享
                ui.share.setText("0");
                ui.sharetl.setText("0");
                ui.switchShare.setChecked(false);
                //点评
                ui.clickComment.setText("0");
                ui.clickCommenttl.setText("0");
                ui.switchClickComment.setChecked(false);
                //返回停留
                ui.backSpeed.setText("3");
                //重启
                ui.restartCount.setText("300");
                //每运行多少分钟休息多少分钟
                ui.meiyunxing.setText("0");
                ui.xiuxi.setText("0");
                //连续多少次点赞失败将休息多少分钟
                ui.lianxu.setText("0");
                ui.jiangxiuxi.setText("0");
                //每多少秒做一次任务
                ui.duoshaomiao.setText("0");
                //版本
                ui.genuine.setChecked(false);
                ui.volcano.setChecked(false);
                ui.selected.setChecked(false);
                ui.shoppingMall.setChecked(false);
                ui.search.setChecked(false);
                ui.qishui.setChecked(false);
                ui.duoshan.setChecked(false);
                ui.gangban.setChecked(true);
                //其他
                ui.selfSearching.setChecked(true);
                ui.delay.setChecked(true);
                ui.rest.setChecked(false);
                ui.zanyan.setChecked(false);
                ui.baiping.setChecked(false);
                ui.jiance.setChecked(false);
                ui.xihuan.setChecked(false);
                ui.liuhen.setChecked(false);
                ui.huoli.setChecked(true);
                ui.xinyouxu.setChecked(false);
                ui.shunxu.setChecked(false);
                ui.xinhun.setChecked(false);
            }
        });
        //封赞配置
        ui.fengzanConfig.on("click", function () {
            {
                //常规清空
                {
                    storages.create(time() + "arr").remove(time() + "arr");
                    storages.create(time() + "allCount").remove(time() + "allCount");
                    storages.create(time() + "allTaskLength_convention").remove(time() + "allTaskLength_convention");
                    storages.create(time() + "likeLength_convention").remove(time() + "likeLength_convention");
                    storages.create(time() + "imageTextLength_convention").remove(time() + "imageTextLength_convention");
                    storages.create(time() + "profilePictureLength_convention").remove(time() + "profilePictureLength_convention");
                    storages.create(time() + "collectLength_convention").remove(time() + "collectLength_convention");
                    storages.create(time() + "commentLength_convention").remove(time() + "commentLength_convention");
                    storages.create(time() + "followWithInterestLength_convention").remove(time() + "followWithInterestLength_convention");
                    storages.create(time() + "privateLetterLength_convention").remove(time() + "privateLetterLength_convention");
                    storages.create(time() + "shareLength_convention").remove(time() + "shareLength_convention");
                    storages.create(time() + "clickCommentLength_convention").remove(time() + "clickCommentLength_convention");
                    storages.create(time() + "recordLikeCount_convention").remove(time() + "recordLikeCount_convention");
                    storages.create(time() + "recordImageTextCount_convention").remove(time() + "recordImageTextCount_convention");
                    storages.create(time() + "recordProfilePictureCount_convention").remove(time() + "recordProfilePictureCount_convention");
                    storages.create(time() + "recordCollectCount_convention").remove(time() + "recordCollectCount_convention");
                    storages.create(time() + "recordCommentCount_convention").remove(time() + "recordCommentCount_convention");
                    storages.create(time() + "recordFollowWithInterestCount_convention").remove(time() + "recordFollowWithInterestCount_convention");
                    storages.create(time() + "recordPrivateLetterCount_convention").remove(time() + "recordPrivateLetterCount_convention");
                    storages.create(time() + "recordShareCount_convention").remove(time() + "recordShareCount_convention");
                    storages.create(time() + "recordClickCommentCount_convention").remove(time() + "recordClickCommentCount_convention");
                    storages.create("imageTextNextTimes").remove("imageTextNextTimes");
                    storages.create("recordLikeTime").remove("recordLikeTime");
                }
    
                //功能
                ui.convention.setChecked(true);
                ui.video.setChecked(false);
                //任务
                //点赞
                ui.like.setText("0");
                ui.liketl.setText("0");
                ui.switchLike.setChecked(false);
                //图文
                ui.imageText.setText("0");
                ui.imageTexttl.setText("0");
                ui.switchImageText.setChecked(false);
                //头像
                ui.profilePicture.setText("0");
                ui.profilePicturetl.setText("0");
                ui.switchProfilePicture.setChecked(false);
                //收藏
                ui.collect.setText("900");
                ui.collecttl.setText("0");
                ui.switchCollect.setChecked(true);
                //评论
                ui.comment.setText("400");
                ui.commenttl.setText("0");
                ui.switchComment.setChecked(true);
                //关注
                ui.followWithInterest.setText("0");
                ui.followWithInteresttl.setText("0");
                ui.switchFollowWithInterest.setChecked(false);
                //私信
                ui.privateLetter.setText("0");
                ui.privateLettertl.setText("0");
                ui.switchPrivateLetter.setChecked(false);
                //分享
                ui.share.setText("900");
                ui.sharetl.setText("150");
                ui.switchShare.setChecked(true);
                //点评
                ui.clickComment.setText("0");
                ui.clickCommenttl.setText("0");
                ui.switchClickComment.setChecked(false);
                //返回停留
                ui.backSpeed.setText("3");
                //重启
                ui.restartCount.setText("300");
                //每运行多少分钟休息多少分钟
                ui.meiyunxing.setText("0");
                ui.xiuxi.setText("0");
                //连续多少次点赞失败将休息多少分钟
                ui.lianxu.setText("0");
                ui.jiangxiuxi.setText("0");
                //每多少秒做一次任务
                ui.duoshaomiao.setText("0");
                //版本
                ui.genuine.setChecked(false);
                ui.volcano.setChecked(false);
                ui.selected.setChecked(false);
                ui.shoppingMall.setChecked(false);
                ui.search.setChecked(false);
                ui.qishui.setChecked(false);
                ui.duoshan.setChecked(true);
                ui.gangban.setChecked(false);
                //其他
                ui.selfSearching.setChecked(true);
                ui.delay.setChecked(true);
                ui.rest.setChecked(false);
                ui.zanyan.setChecked(false);
                ui.baiping.setChecked(false);
                ui.jiance.setChecked(false);
                ui.xihuan.setChecked(false);
                ui.liuhen.setChecked(false);
                ui.huoli.setChecked(false);
                ui.xinyouxu.setChecked(false);
                ui.shunxu.setChecked(false);
                ui.xinhun.setChecked(false);
            }
        });
        //留痕配置
        ui.liuhenConfig.on("click", function () {
            {
                //常规清空
                {
                    storages.create(time() + "arr").remove(time() + "arr");
                    storages.create(time() + "allCount").remove(time() + "allCount");
                    storages.create(time() + "allTaskLength_convention").remove(time() + "allTaskLength_convention");
                    storages.create(time() + "likeLength_convention").remove(time() + "likeLength_convention");
                    storages.create(time() + "imageTextLength_convention").remove(time() + "imageTextLength_convention");
                    storages.create(time() + "profilePictureLength_convention").remove(time() + "profilePictureLength_convention");
                    storages.create(time() + "collectLength_convention").remove(time() + "collectLength_convention");
                    storages.create(time() + "commentLength_convention").remove(time() + "commentLength_convention");
                    storages.create(time() + "followWithInterestLength_convention").remove(time() + "followWithInterestLength_convention");
                    storages.create(time() + "privateLetterLength_convention").remove(time() + "privateLetterLength_convention");
                    storages.create(time() + "shareLength_convention").remove(time() + "shareLength_convention");
                    storages.create(time() + "clickCommentLength_convention").remove(time() + "clickCommentLength_convention");
                    storages.create(time() + "recordLikeCount_convention").remove(time() + "recordLikeCount_convention");
                    storages.create(time() + "recordImageTextCount_convention").remove(time() + "recordImageTextCount_convention");
                    storages.create(time() + "recordProfilePictureCount_convention").remove(time() + "recordProfilePictureCount_convention");
                    storages.create(time() + "recordCollectCount_convention").remove(time() + "recordCollectCount_convention");
                    storages.create(time() + "recordCommentCount_convention").remove(time() + "recordCommentCount_convention");
                    storages.create(time() + "recordFollowWithInterestCount_convention").remove(time() + "recordFollowWithInterestCount_convention");
                    storages.create(time() + "recordPrivateLetterCount_convention").remove(time() + "recordPrivateLetterCount_convention");
                    storages.create(time() + "recordShareCount_convention").remove(time() + "recordShareCount_convention");
                    storages.create(time() + "recordClickCommentCount_convention").remove(time() + "recordClickCommentCount_convention");
                    storages.create("imageTextNextTimes").remove("imageTextNextTimes");
                    storages.create("recordLikeTime").remove("recordLikeTime");
                }
    
                //功能
                ui.convention.setChecked(true);
                ui.video.setChecked(false);
                //任务
                //点赞
                ui.like.setText("0");
                ui.liketl.setText("0");
                ui.switchLike.setChecked(false);
                //图文
                ui.imageText.setText("0");
                ui.imageTexttl.setText("0");
                ui.switchImageText.setChecked(false);
                //头像
                ui.profilePicture.setText("0");
                ui.profilePicturetl.setText("0");
                ui.switchProfilePicture.setChecked(false);
                //收藏
                ui.collect.setText("0");
                ui.collecttl.setText("0");
                ui.switchCollect.setChecked(false);
                //评论
                ui.comment.setText("0");
                ui.commenttl.setText("0");
                ui.switchComment.setChecked(false);
                //关注
                ui.followWithInterest.setText("0");
                ui.followWithInteresttl.setText("0");
                ui.switchFollowWithInterest.setChecked(false);
                //私信
                ui.privateLetter.setText("0");
                ui.privateLettertl.setText("0");
                ui.switchPrivateLetter.setChecked(false);
                //分享
                ui.share.setText("0");
                ui.sharetl.setText("0");
                ui.switchShare.setChecked(false);
                //点评
                ui.clickComment.setText("0");
                ui.clickCommenttl.setText("0");
                ui.switchClickComment.setChecked(false);
                //返回停留
                ui.backSpeed.setText("3");
                //重启
                ui.restartCount.setText("300");
                //每运行多少分钟休息多少分钟
                ui.meiyunxing.setText("0");
                ui.xiuxi.setText("0");
                //连续多少次点赞失败将休息多少分钟
                ui.lianxu.setText("0");
                ui.jiangxiuxi.setText("0");
                //每多少秒做一次任务
                ui.duoshaomiao.setText("0");
                //版本
                ui.genuine.setChecked(false);
                ui.volcano.setChecked(false);
                ui.selected.setChecked(false);
                ui.shoppingMall.setChecked(false);
                ui.search.setChecked(false);
                ui.qishui.setChecked(false);
                ui.duoshan.setChecked(true);
                ui.gangban.setChecked(false);
                //其他
                ui.selfSearching.setChecked(true);
                ui.delay.setChecked(true);
                ui.rest.setChecked(false);
                ui.zanyan.setChecked(false);
                ui.baiping.setChecked(false);
                ui.jiance.setChecked(false);
                ui.xihuan.setChecked(false);
                ui.liuhen.setChecked(true);
                ui.huoli.setChecked(false);
                ui.xinyouxu.setChecked(false);
                ui.shunxu.setChecked(false);
                ui.xinhun.setChecked(false);
            }
        });
        //清空任务
        ui.clearTask.on("click", function () {
            //常规清空
    
        storages.create(time() + "arr").remove(time() + "arr");
        storages.create(time() + "allCount").remove(time() + "allCount");
        storages.create(time() + "allTaskLength_convention").remove(time() + "allTaskLength_convention");
        storages.create(time() + "likeLength_convention").remove(time() + "likeLength_convention");
        storages.create(time() + "imageTextLength_convention").remove(time() + "imageTextLength_convention");
        storages.create(time() + "profilePictureLength_convention").remove(time() + "profilePictureLength_convention");
        storages.create(time() + "collectLength_convention").remove(time() + "collectLength_convention");
        storages.create(time() + "commentLength_convention").remove(time() + "commentLength_convention");
        storages.create(time() + "followWithInterestLength_convention").remove(time() + "followWithInterestLength_convention");
        storages.create(time() + "privateLetterLength_convention").remove(time() + "privateLetterLength_convention");
        storages.create(time() + "shareLength_convention").remove(time() + "shareLength_convention");
        storages.create(time() + "clickCommentLength_convention").remove(time() + "clickCommentLength_convention");
        storages.create(time() + "recordLikeCount_convention").remove(time() + "recordLikeCount_convention");
        storages.create(time() + "recordImageTextCount_convention").remove(time() + "recordImageTextCount_convention");
        storages.create(time() + "recordProfilePictureCount_convention").remove(time() + "recordProfilePictureCount_convention");
        storages.create(time() + "recordCollectCount_convention").remove(time() + "recordCollectCount_convention");
        storages.create(time() + "recordCommentCount_convention").remove(time() + "recordCommentCount_convention");
        storages.create(time() + "recordFollowWithInterestCount_convention").remove(time() + "recordFollowWithInterestCount_convention");
        storages.create(time() + "recordPrivateLetterCount_convention").remove(time() + "recordPrivateLetterCount_convention");
        storages.create(time() + "recordShareCount_convention").remove(time() + "recordShareCount_convention");
        storages.create(time() + "recordClickCommentCount_convention").remove(time() + "recordClickCommentCount_convention");
        storages.create("imageTextNextTimes").remove("imageTextNextTimes");
        storages.create("recordLikeTime").remove("recordLikeTime");
        //storages.create("oldVersion").remove("oldVersion");
       //storages.create("fanDifference").remove("fanDifference");
       
       exit();
        });
    };
    
    function sleep(ms) {
        var ms_ = new Date().getTime();
        while (true) {
            if ((new Date().getTime() - ms_) > ms) {
                break;
            }
        }
    };
    //主程序
    function main() {
        threads.start(function () {
            while (true) {
                if (text("禁止").findOnce() && text("允许").findOnce()) {
                    if (!text("位置信息").findOnce()) {
                        var bound = text("允许").findOne().bounds();
                        var x = bound.centerX();
                        var y = bound.centerY();
                        sleep(500);
                        click(x, y);
                        sleep(3000);
                        break;
                    }
                }
                if (text("取消").findOnce() && text("立即开始").findOnce()) {
                    var bound = text("立即开始").findOne().bounds();
                    var x = bound.centerX();
                    var y = bound.centerY();
                    sleep(500);
                    click(x, y);
                    sleep(3000);
                    break;
                }
                if (text("取消").findOnce() && text("允许").findOnce()) {
                    if (!text("位置信息").findOnce()) {
                        var bound = text("允许").findOne().bounds();
                        var x = bound.centerX();
                        var y = bound.centerY();
                        sleep(500);
                        click(x, y);
                        sleep(3000);
                        break;
                    }
                }
            }
        });
        threads.start(function () {
            requestScreenCapture();
            sleep(1000);
            let removeObstacles = require('./script/dyzf/removeObstacles.js');
            removeObstacles.removeObstacles();
        });
        threads.start(function () {
        //悬浮图片
            var w = floaty.window(
                <vertical gravity="center">
                    <img id="end" src="@drawable/ic_highlight_off_black_48dp" tint="#FFC107" w="40" h="40" alpha="0.5" />
                </vertical>
            );
            w.setPosition(0, device.height / 2);
            w.end.on("click", function () {
                log("结束运行");
                exit();
            });
    
            log("开始运行");
            sleep(1000);
            log("请勿连点'开始'按钮");
            if (String(ui.AuthorizationCode.getText()).split(" ").length > 1) {
                alert('卡密输入有空格，请删除空格部分再启动！');
                exit();
            }
            {
                //let rightKey = require('rightKey.js');
               //rightKey.rightKey(String(ui.AuthorizationCode.getText()));
            }
            let countRestart = 0;
            var convention = require("./script/dyzf/convention.js");
            var video = require('./script/dyzf/video.js');
            while (true) {
                //版本
                {
                    if (ui.volcano.checked) {
                        storages.create("appName").put("appName", "抖音火山版");
                        storages.create("whatHao").put("whatHao", "火山号");
                        storages.create("idName").put("idName", "com.ss.android.ugc.live");
                    } else if (ui.selected.checked) {
                        storages.create("appName").put("appName", "抖音精选");
                        storages.create("whatHao").put("whatHao", "抖音号");
                        storages.create("idName").put("idName", "com.ss.android.yumme.video");
                    } else if (ui.shoppingMall.checked) {
                        storages.create("appName").put("appName", "抖音商城");
                        storages.create("whatHao").put("whatHao", "抖音号");
                        storages.create("idName").put("idName", "com.ss.android.ugc.livelite");
                    } else if (ui.search.checked) {
                        storages.create("appName").put("appName", "com.ss.android.ugc.aweme.hubble");
                        storages.create("whatHao").put("whatHao", "抖音号");
                        storages.create("idName").put("idName", "com.ss.android.ugc.aweme.hubble");
                    } else if (ui.qishui.checked) {
                        storages.create("appName").put("appName", "汽水音乐");
                    } else if (ui.duoshan.checked) {
                        storages.create("appName").put("appName", "多闪");
                        storages.create("whatHao").put("whatHao", "抖音号");
                        storages.create("idName").put("idName", "my.maya.android");
                    } else if (ui.gangban.checked) {
                        storages.create("appName").put("appName", "com.ss.android.ugc.aweme.mobile");
                        storages.create("whatHao").put("whatHao", "抖音号");
                        storages.create("idName").put("idName", "com.ss.android.ugc.aweme.mobile");
                    } else {
                        storages.create("appName").put("appName", "抖音");
                        storages.create("whatHao").put("whatHao", "抖音号");
                        storages.create("idName").put("idName", "com.ss.android.ugc.aweme");
                    }
                }
                //功能
                {
                    {
                        //常规
                        {
                            if (ui.lingdian.checked || ui.yidian.checked || ui.erdian.checked || ui.sandian.checked || ui.sidian.checked || ui.wudian.checked || ui.liudian.checked || ui.qidian.checked || ui.badian.checked || ui.jiudian.checked || ui.shidian.checked || ui.shiyidian.checked || ui.shierdian.checked || ui.shisandian.checked || ui.shisidian.checked || ui.shiwudian.checked || ui.shiliudian.checked || ui.shiqidian.checked || ui.shibadian.checked || ui.shijiudian.checked || ui.ershidian.checked || ui.ershiyidian.checked || ui.ershierdian.checked || ui.ershisandian.checked) {
                                if (((new Date().getHours() == 0) && ui.lingdian.checked) || ((new Date().getHours() == 1) && ui.yidian.checked) || ((new Date().getHours() == 2) && ui.erdian.checked) || ((new Date().getHours() == 3) && ui.sandian.checked) || ((new Date().getHours() == 4) && ui.sidian.checked) || ((new Date().getHours() == 5) && ui.wudian.checked) || ((new Date().getHours() == 6) && ui.liudian.checked) || ((new Date().getHours() == 7) && ui.qidian.checked) || ((new Date().getHours() == 8) && ui.badian.checked) || ((new Date().getHours() == 9) && ui.jiudian.checked) || ((new Date().getHours() == 10) && ui.shidian.checked) || ((new Date().getHours() == 11) && ui.shiyidian.checked) || ((new Date().getHours() == 12) && ui.shierdian.checked) || ((new Date().getHours() == 13) && ui.shisandian.checked) || ((new Date().getHours() == 14) && ui.shisidian.checked) || ((new Date().getHours() == 15) && ui.shiwudian.checked) || ((new Date().getHours() == 16) && ui.shiliudian.checked) || ((new Date().getHours() == 17) && ui.shiqidian.checked) || ((new Date().getHours() == 18) && ui.shibadian.checked) || ((new Date().getHours() == 19) && ui.shijiudian.checked) || ((new Date().getHours() == 20) && ui.ershidian.checked) || ((new Date().getHours() == 21) && ui.ershiyidian.checked) || ((new Date().getHours() == 22) && ui.ershierdian.checked) || ((new Date().getHours() == 23) && ui.ershisandian.checked)) {
                                    if (ui.convention.checked) {
                                        storages.create("recordFans").put("recordFans", 0);
                                        //执行任务
                                        log("开始常规1");
                                        convention.convention(AuthorizationCode);
                                        
                                    }
                                } else {
                                    log("未在可运行时间内");
                                    sleep(3000);
                                    home();
                                    sleep(3000);
                                }
                            } else {
                                if (ui.convention.checked) {
                                    storages.create("recordFans").put("recordFans", 0);
                                    //执行任务
                                    log("开始常规2");
                                    convention.convention(AuthorizationCode);
                                }
                            }
                        }
                        //视频
                        {
                            if (ui.video.checked) {
                                if ((storages.create("countNone").get("countNone") == undefined) || (storages.create("countNone").get("countNone") == 0) || (!ui.convention.checked)) {
                                    log("开始视频");
                                    video.video();
                                }
                            }
                        }
                    }
                }
                //异常重启
                {
                    if (ui.lingdian.checked || ui.yidian.checked || ui.erdian.checked || ui.sandian.checked || ui.sidian.checked || ui.wudian.checked || ui.liudian.checked || ui.qidian.checked || ui.badian.checked || ui.jiudian.checked || ui.shidian.checked || ui.shiyidian.checked || ui.shierdian.checked || ui.shisandian.checked || ui.shisidian.checked || ui.shiwudian.checked || ui.shiliudian.checked || ui.shiqidian.checked || ui.shibadian.checked || ui.shijiudian.checked || ui.ershidian.checked || ui.ershiyidian.checked || ui.ershierdian.checked || ui.ershisandian.checked) {
    
                    } else {
                        if (storages.create(time() + "arr").get(time() + "arr") != undefined) {
                            if (storages.create(time() + "arr").get(time() + "arr").length != 0) {
                                countRestart++;
                                if (countRestart >= 100) {
                                    alert("该设备正在反复重启！请检查网络是否正常！");
                                    exit();
                                }
                            }
                        }
                    }
                }
                sleep(1000);
            }
        });
    };
    
    
    
    
    // 判断是否有读取应用列表的权限
    function hasAppListPermission() {
        if (device.sdkInt >= 30) { // Android 11 (API 30) 及以上
            try {
                // 尝试获取应用列表，如果无权限会抛出 SecurityException
                let pm = context.getPackageManager();
                pm.getInstalledApplications(0);
                return true;
            } catch (e) {
                return false;
            }
        } else {
            // Android 10 及以下默认有权限
            return true;
        }
    }
    
    // 申请权限（仅 Android 11+ 需要）
    function requestAppListPermission() {
        if (device.sdkInt >= 30) {
            // 尝试申请 QUERY_ALL_PACKAGES 权限（部分设备需要手动在设置开启）
            app.startActivity({
                action: "android.settings.ACTION_MANAGE_OVERLAY_PERMISSION",
                data: "package:" + context.getPackageName()
            });
            toast("请在设置中允许「读取应用列表」权限");
        }
    }
    
    //let km = ui.AuthorizationCode.getText();
    ui.start.on("click", function () {
        // 获取当前时间戳（秒级）
    let timestamp = Math.floor(new Date().getTime() / 1000);
        if(pjysdk.login_result.expires_ts >= timestamp){
    
        }else{
            return;
        }
    // 使用示例
    if (!hasAppListPermission()) {
        toast("需要读取应用列表权限");
        requestAppListPermission();
    }
    
        // 登录成功，后面写你的业务代码
        threads.shutDownAll();
        //日志窗口
        {
            var win = floaty.rawWindow(
                <vertical>
                    <text text="" id="console2" h="auto" w="150" bg="#88000000" textColor="#00ffff" />
                    <com.stardust.autojs.core.console.ConsoleView id="console1" h="150" w="150" bg="#88000000" />
                </vertical>
            );
            //设置位置
            win.setPosition(0, device.height / 35);
            //设置可触摸
            win.setTouchable(false);
            //去除控制台按钮
            win.console1.setInputEnabled(false);
            //加载日志信息到ui控制台
            win.console1.setConsole(runtime.console);
        }
        //手机权限
        {
            if (floaty.checkPermission() == false) {
                log("请先开启悬浮窗权限！")
                return;
            }
            //程序开始运行之前判断无障碍服务
            if (auto.service == null) {
                log("请先开启无障碍服务！");
                return;
            }
        }
        //保存配置
        {
            storages.create("duoshaomiao").put("duoshaomiao", Number(ui.duoshaomiao.getText()));
            storages.create('xinhun').put('xinhun', ui.xinhun.checked);
            storages.create('shunxu').put('shunxu', ui.shunxu.checked);
            storages.create('once').remove('once');
            storages.create('once1').remove('once1');
            storages.create('once2').remove('once2');
            storages.create('once3').remove('once3');
            storages.create('once4').remove('once4');
            storages.create('allTaskLength_convention').remove('allTaskLength_convention');
            storages.create("gangban").put("gangban", ui.gangban.checked);
            storages.create("duoshan").put("duoshan", ui.duoshan.checked);
            storages.create("xinyouxu").put("xinyouxu", ui.xinyouxu.checked);
            storages.create("huoli").put("huoli", ui.huoli.checked);
            storages.create("liunan").put("liunan", ui.liunan.checked);
            storages.create("liunv").put("liunv", ui.liunv.checked);
            storages.create("liuhen").put("liuhen", ui.liuhen.checked);
            storages.create("video").put("video", ui.video.checked);
            storages.create("lingdian").put("lingdian", ui.lingdian.checked);
            storages.create("yidian").put("yidian", ui.yidian.checked);
            storages.create("erdian").put("erdian", ui.erdian.checked);
            storages.create("sandian").put("sandian", ui.sandian.checked);
            storages.create("sidian").put("sidian", ui.sidian.checked);
            storages.create("wudian").put("wudian", ui.wudian.checked);
            storages.create("liudian").put("liudian", ui.liudian.checked);
            storages.create("qidian").put("qidian", ui.qidian.checked);
            storages.create("badian").put("badian", ui.badian.checked);
            storages.create("jiudian").put("jiudian", ui.jiudian.checked);
            storages.create("shidian").put("shidian", ui.shidian.checked);
            storages.create("shiyidian").put("shiyidian", ui.shiyidian.checked);
            storages.create("shierdian").put("shierdian", ui.shierdian.checked);
            storages.create("shisandian").put("shisandian", ui.shisandian.checked);
            storages.create("shisidian").put("shisidian", ui.shisidian.checked);
            storages.create("shiwudian").put("shiwudian", ui.shiwudian.checked);
            storages.create("shiliudian").put("shiliudian", ui.shiliudian.checked);
            storages.create("shiqidian").put("shiqidian", ui.shiqidian.checked);
            storages.create("shibadian").put("shibadian", ui.shibadian.checked);
            storages.create("shijiudian").put("shijiudian", ui.shijiudian.checked);
            storages.create("ershidian").put("ershidian", ui.ershidian.checked);
            storages.create("ershiyidian").put("ershiyidian", ui.ershiyidian.checked);
            storages.create("ershierdian").put("ershierdian", ui.ershierdian.checked);
            storages.create("ershisandian").put("ershisandian", ui.ershisandian.checked);
            storages.create("xihuan").put("xihuan", ui.xihuan.checked);
            storages.create("qishui").put("qishui", ui.qishui.checked);
            storages.create("baiping").put("baiping", ui.baiping.checked);
            storages.create("jiance").put("jiance", ui.jiance.checked);
            storages.create("rest").put("rest", ui.rest.checked);
            storages.create("lianxu").put("lianxu", Number(ui.lianxu.getText()));
            storages.create("jiangxiuxi").put("jiangxiuxi", Number(ui.jiangxiuxi.getText()));
            storages.create("meiyunxing").put("meiyunxing", Number(ui.meiyunxing.getText()));
            storages.create('number').put('number', '212371x10388x6954');
            storages.create("xiuxi").put("xiuxi", Number(ui.xiuxi.getText()));
            storages.create("zanyan").put("zanyan", ui.zanyan.checked);
            storages.create("selfSearching").put("selfSearching", ui.selfSearching.checked);
            storages.create("ticketKey").put("ticketKey", String(ui.AuthorizationCode.getText()));
            storages.create("thing").put("thing", String(ui.AuthorizationCode.getText()));
            storages.create("someString").remove("someString");
            storages.create("convention").put("convention", ui.convention.checked);
            storages.create("genuine").put("genuine", ui.genuine.checked);
            storages.create("volcano").put("volcano", ui.volcano.checked);
            storages.create("selected").put("selected", ui.selected.checked);
            storages.create("like").put("like", Number(ui.like.getText()));
            storages.create("imageText").put("imageText", Number(ui.imageText.getText()));
            storages.create("profilePicture").put("profilePicture", Number(ui.profilePicture.getText()));
            storages.create("collect").put("collect", Number(ui.collect.getText()));
            storages.create("comment").put("comment", Number(ui.comment.getText()));
            storages.create("followWithInterest").put("followWithInterest", Number(ui.followWithInterest.getText()));
            storages.create("privateLetter").put("privateLetter", Number(ui.privateLetter.getText()));
            storages.create("share").put("share", Number(ui.share.getText()));
            storages.create("restartCount").put("restartCount", Number(ui.restartCount.getText()));
            storages.create("sharetl").put("sharetl", Number(ui.sharetl.getText()));
            storages.create("commenttl").put("commenttl", Number(ui.commenttl.getText()));
            storages.create("liketl").put("liketl", Number(ui.liketl.getText()));
            storages.create("privateLettertl").put("privateLettertl", Number(ui.privateLettertl.getText()));
            storages.create("followWithInteresttl").put("followWithInteresttl", Number(ui.followWithInteresttl.getText()));
            storages.create("collecttl").put("collecttl", Number(ui.collecttl.getText()));
            storages.create("profilePicturetl").put("profilePicturetl", Number(ui.profilePicturetl.getText()));
            storages.create("imageTexttl").put("imageTexttl", Number(ui.imageTexttl.getText()));
            storages.create("cgtl").put("cgtl", Number(ui.cgtl.getText()));
            storages.create("shoppingMall").put("shoppingMall", ui.shoppingMall.checked);
            storages.create("backSpeed").put("backSpeed", Number(ui.backSpeed.getText()));
            storages.create("delay").put("delay", ui.delay.checked);
            storages.create("clickComment").put("clickComment", Number(ui.clickComment.getText()));
            storages.create("clickCommenttl").put("clickCommenttl", Number(ui.clickCommenttl.getText()));
            storages.create("search").put("search", ui.search.checked);
            storages.create("switchLike").put("switchLike", ui.switchLike.checked);
            storages.create("switchImageText").put("switchImageText", ui.switchImageText.checked);
            storages.create("switchProfilePicture").put("switchProfilePicture", ui.switchProfilePicture.checked);
            storages.create("switchCollect").put("switchCollect", ui.switchCollect.checked);
            storages.create("switchComment").put("switchComment", ui.switchComment.checked);
            storages.create("switchFollowWithInterest").put("switchFollowWithInterest", ui.switchFollowWithInterest.checked);
            storages.create("switchPrivateLetter").put("switchPrivateLetter", ui.switchPrivateLetter.checked);
            storages.create("switchShare").put("switchShare", ui.switchShare.checked);
            storages.create("switchClickComment").put("switchClickComment", ui.switchClickComment.checked);
        }
        //保持
        {
            let t = setInterval(() => {
                ui.run(() => {
                    //第一窗口集
                    {
                        let firstWindow = require('./script/dyzf/firstWindow.js');
                        let finalString = firstWindow.firstWindow()
                        win.console2.setText(finalString);
                    }
                });
            }, 1000);
    
            events.on("exit", () => {
                clearInterval(t);
            });
    
        }
        //主程序
        {
            storages.create("meiyunxingxiuxi").put("meiyunxingxiuxi", storages.create("meiyunxing").get("meiyunxing") * 60 * 1000 + new Date().getTime());
            storages.create("recordLikesDo").put("recordLikesDo", 0);
            main();
        }
      
    
    });
    }
},1000);

