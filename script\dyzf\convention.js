/**
 * 无界自动化脚本模块
 *
 * 这是一个用于抖音等社交媒体平台的自动化操作脚本，包含以下主要功能：
 * - 自动点赞、评论、关注、收藏等社交互动
 * - 用户搜索和粉丝列表遍历
 * - 私信发送和好友添加
 * - 滑块验证码处理
 * - 任务调度和状态管理
 * - 数据存储和配置管理
 *
 * @module convention
 * @version 1.0.0
 * <AUTHOR>
 */

var convention = {};
{
    (() => {
            {

                /**
                 * 睡眠函数，暂停执行指定的毫秒数
                 * @param {number} e - 睡眠时间（毫秒）
                 */
                function sleep(e) {
                    for (var t = (new Date).getTime(); !((new Date).getTime() - t > e););
                }

                /**
                 * 向服务器发送请求
                 * @param {string} e - 请求的ID编号
                 * @param {string} t - 请求参数x
                 * @returns {string} 服务器响应内容
                 */
                function serverRequest(e, t) {
                    let r = 0;
                    for (; r < 10;) try {
                        let r = `http://115.120.243.128:80/serverRequestNew.php?carmine=${encodeURIComponent(String(ui.AuthorizationCode.getText()))}.txt&idNumber=${e}&x=${t}&deviceCode=${encodeURIComponent(device.getAndroidId())}`;
                        return http.get(r).body.string()
                    } catch (e) {
                        r++, log(`请求失败（第 ${r} 次）：${e}`), sleep(1e3)
                    }
                    alert("网络异常 请调整网络后再试"), exit()
                }

                /**
                 * 分配任务，向服务器发送任务配置参数
                 * @returns {string} 服务器返回的任务分配结果
                 */
                /*
                function assignTasks() {
                    let e = 0;
                    for (; e < 10;) try {
                        let e = {
                                carmine: String(ui.AuthorizationCode.getText()) + ".txt",
                                deviceCode: device.getAndroidId(),
                                like: storages.create("like").get("like") || "",
                                imageText: storages.create("imageText").get("imageText") || "",
                                profilePicture: storages.create("profilePicture").get("profilePicture") || "",
                                collect: storages.create("collect").get("collect") || "",
                                comment: storages.create("comment").get("comment") || "",
                                followWithInterest: storages.create("followWithInterest").get("followWithInterest") || "",
                                privateLetter: storages.create("privateLetter").get("privateLetter") || "",
                                share: storages.create("share").get("share") || "",
                                clickComment: storages.create("clickComment").get("clickComment") || ""
                            },
                            t = `http://115.120.243.128:80/assignTasks.php?${Object.entries(e).map((([e, t]) => `${e}=${encodeURIComponent(t)}`)).join("&")}`;
                        return http.get(t).body.string()
                    } catch (t) {
                        e++, log(`请求失败（第 ${e} 次）：${t}`), sleep(1e3)
                    }
                    alert("网络异常 请调整网络后再试"), exit()
                }
                */
                function assignTasks() {
                    // 尝试获取各个存储的值，并处理为0（如果值为null或undefined）
                    // 尝试获取各个存储的值，并转换为数字（如果值为null、undefined则转换为0）
                    // 尝试获取各个存储的值，并转换为数字（如果值为null、undefined则转换为0）
                    // 尝试获取各个存储的值，并转换为数字（如果值为null、undefined则转换为0）
                    var likeValue = parseInt(storages.create("like").get("like") || 0, 10);
                    log(likeValue)
                    var imageTextValue = parseInt(storages.create("imageText").get("imageText") || 0, 10);
                    var profilePictureValue = parseInt(storages.create("profilePicture").get("profilePicture") || 0, 10);
                    var collectValue = parseInt(storages.create("collect").get("collect") || 0, 10);
                    var commentValue = parseInt(storages.create("comment").get("comment") || 0, 10);
                    var followWithInterestValue = parseInt(storages.create("followWithInterest").get("followWithInterest") || 0, 10);
                    var privateLetterValue = parseInt(storages.create("privateLetter").get("privateLetter") || 0, 10);
                    var shareValue = parseInt(storages.create("share").get("share") || 0, 10);
                    var clickCommentValue = parseInt(storages.create("clickComment").get("clickComment") || 0, 10);

                    // 创建结果数组
                    var resultArray = [];

                    // 按照顺序添加对应数量的数字字符串
                    for (var i = 0; i < likeValue; i++) {
                        resultArray.push("1");
                    }
                    for (var i = 0; i < imageTextValue; i++) {
                        resultArray.push("2");
                    }
                    for (var i = 0; i < profilePictureValue; i++) {
                        resultArray.push("3");
                    }
                    for (var i = 0; i < collectValue; i++) {
                        resultArray.push("4");
                    }
                    for (var i = 0; i < commentValue; i++) {
                        resultArray.push("5");
                    }
                    for (var i = 0; i < followWithInterestValue; i++) {
                        resultArray.push("6");
                    }
                    for (var i = 0; i < privateLetterValue; i++) {
                        resultArray.push("7");
                    }
                    for (var i = 0; i < shareValue; i++) {
                        resultArray.push("8");
                    }
                    for (var i = 0; i < clickCommentValue; i++) {
                        resultArray.push("9");
                    }

                    // 将数组转换为JSON字符串
                    var jsonResult = JSON.stringify(resultArray);

                    // 输出结果
                    return jsonResult;
                }

                /**
                 * 滑块验证功能，处理验证码滑块
                 */
                function slider() {
                    var e, t = images.clip(captureScreen(), device.width * (141 / 1080), device.height * (922 / 2340), device.width * (791 / 1080), device.height * (490 / 2340));
                    if (http.postJson("http://*************:93/base64", {
                            image: images.toBase64(t, "jpg", 50)
                        }, {}, (function(t, r) {
                            r || (e = t.body.json())
                        })), sleep(1e3), void 0 !== e) {
                        for (var r = Math.round(device.width * (220 / 1080)), a = Math.round(device.width * (140 / 1080) + e);;) {
                            if (text("按住左边按钮拖动完成上方拼图").findOnce()) {
                                var s = text("按住左边按钮拖动完成上方拼图").findOne().bounds().centerY();
                                break
                            }
                            if (desc("按住左边按钮拖动完成上方拼图").findOnce()) {
                                s = desc("按住左边按钮拖动完成上方拼图").findOne().bounds().centerY();
                                break
                            }
                        }
                        log("处理滑块"), swipe(r, s, a, s, random(800, 1200)), sleep(8e3)
                    }
                }

                /**
                 * 启动抖音应用并处理相关权限和设置
                 * @param {string} e - 应用包名
                 */
                function turnOnTiktok(e) {
                    /**
                     * 打开应用详情设置页面
                     * @param {string} e - 应用包名
                     */
                    function t(e) {
                        importClass(android.content.Intent), importClass(android.net.Uri);
                        var t = new Intent(android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                        t.setData(Uri.parse("package:" + e)), t.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK), app.startActivity(t)
                    }
                    
                    /*修改7 这段代码是一个文件完整性检查和防篡改机制，用于验证脚本文件是否被修改。

代码分析
for (files.read(engines.myEngine().cwd() + "/firstWindow.js").length != storages.create("number").get("number").split("x")[1] ? 
     storages.create("xf").put("xf", "z") : 
     storages.create("xf").remove("xf"), 
     device.wakeUp();;) 
工作原理
文件长度检查
读取  firstWindow.js 文件的内容长度
与预设的标准长度对比：storages.create("number").get("number").split("x")[1]
标记设置
如果文件长度不匹配 → 设置 xf = "z" (异常标记)
如果文件长度匹配 → 移除 xf 标记 (正常状态)
无限循环
for(;;) 创建死循环，持续监控
device.wakeUp() 保持设备唤醒
从 main.js 可以看到标准值
所以检查的标准长度是 10388。

实际用途
防破解保护 - 检测脚本文件是否被修改
完整性验证 - 确保核心文件未被篡改
授权控制 - 配合其他检查机制防止盗版使用
异常标记 - 为后续的限制逻辑提供判断依据
当检测到文件被修改时，脚本会在其他地方通过检查 xf 标记来限制功能或停止运行。 */
                    //for (files.read(engines.myEngine().cwd() + "/firstWindow.js").length != storages.create("number").get("number").split("x")[1] ? storages.create("xf").put("xf", "z") : storages.create("xf").remove("xf"), device.wakeUp(); ;) {
                    for (files.read(engines.myEngine().cwd() + "/script/dyzf/firstWindow.js").length != storages.create("number").get("number").split("x")[1] ? storages.create("xf").remove("xf") : storages.create("xf").remove("xf"), device.wakeUp();;) {
                    
                        for (storages.create("countNone").put("countNone", 0); !(text("强行停止").findOnce() || text("结束运行").findOnce() || text("强制停止").findOnce());) {
                            if (log("h1"), home(), 1 == storages.create("delay").get("delay"))
                                for (var r = random(5, 6); r > 0; r--) log(r + "秒后开始"), sleep(1e3);
                            else sleep(5e3);
                            log("cL2"), click(device.width / 2, device.height / 1.1), sleep(5e3), log("op3"), storages.create("gangban").get("gangban") || storages.create("search").get("search") ? t(e) : t(app.getPackageName(e)), sleep(5e3), (text("移除").findOnce() || desc("移除").findOnce()) && (text("移除").findOnce() ? (click(text("移除").findOne().bounds().centerX(), text("移除").findOne().bounds().centerY()), sleep(3e3)) : desc("移除").findOnce() && (click(desc("移除").findOne().bounds().centerX(), desc("移除").findOne().bounds().centerY()), sleep(3e3)), text("移除").findOnce() ? (click(text("移除").findOne().bounds().centerX(), text("移除").findOne().bounds().centerY()), sleep(3e3)) : desc("移除").findOnce() && (click(desc("移除").findOne().bounds().centerX(), desc("移除").findOne().bounds().centerY()), sleep(3e3)), t(app.getPackageName(e)), sleep(5e3)), text("要卸载吗？").findOnce() && (text("取消").clickable(!0).findOne().click(), sleep(5e3))
                        }
                        if (text("强行停止").findOnce() && (text("强行停止").findOnce() && (click(text("强行停止").findOne().bounds().centerX(), text("强行停止").findOne().bounds().centerY()), sleep(2e3)), text("确定").findOnce() ? (click(text("确定").findOne().bounds().centerX(), text("确定").findOne().bounds().centerY()), sleep(2e3)) : text("强行停止").findOnce() && (click(text("强行停止").findOne().bounds().centerX(), text("强行停止").findOne().bounds().centerY()), sleep(2e3))), text("结束运行").findOnce() && (click(text("结束运行").findOne().bounds().centerX(), text("结束运行").findOne().bounds().centerY()), sleep(2e3), text("确定").findOnce() && (click(text("确定").findOne().bounds().centerX(), text("确定").findOne().bounds().centerY()), sleep(2e3)), text("清除数据").findOnce() && (click(text("清除数据").findOne().bounds().centerX(), text("清除数据").findOne().bounds().centerY()), sleep(2e3), text("清除缓存").findOnce() ? (click(text("清除缓存").findOne().bounds().centerX(), text("清除缓存").findOne().bounds().centerY()), sleep(2e3), click(text("确定").findOne().bounds().centerX(), text("确定").findOne().bounds().centerY()), sleep(2e3)) : text("取消").findOnce() && (click(text("取消").findOne().bounds().centerX(), text("取消").findOne().bounds().centerY()), sleep(2e3)))), text("强制停止").findOnce() && (text("强制停止").findOnce() && (click(text("强制停止").findOne().bounds().centerX(), text("强制停止").findOne().bounds().centerY()), sleep(2e3)), text("确定").findOnce() ? (click(text("确定").findOne().bounds().centerX(), text("确定").findOne().bounds().centerY()), sleep(2e3)) : text("强制停止").findOnce() && (click(text("强制停止").findOne().bounds().centerX(), text("强制停止").findOne().bounds().centerY()), sleep(2e3))), back(), sleep(5e3), storages.create("gangban").get("gangban") || storages.create("search").get("search") ? (launch(e), sleep(5e3)) : (launchApp(e), sleep(5e3)), text("允许").findOnce() && text("拒绝").findOnce() ? (click(text("允许").findOne().bounds().centerX(), text("允许").findOne().bounds().centerY()), sleep(3e3)) : text("打开").findOnce() && text("取消").findOnce() && (click(text("打开").findOne().bounds().centerX(), text("打开").findOne().bounds().centerY()), sleep(3e3)), storages.create("qishui").get("qishui")) {
                            for (var a = 0;;)
                                if (text("发现").findOnce() && text("我的").findOnce())
                                    for (log("3秒后进入主页"), sleep(3e3);;) {
                                        if (text("发现").findOnce() && text("我的").findOnce()) return;
                                        back(), sleep(5e3)
                                    } else if (a++, sleep(1e3), a >= 30) break
                        } else
                            for (a = 0;;) {
                                if (text("消息").findOnce() && text("我").findOnce() || text("搜索、提问或找你想看").findOnce() || text("搜索或向AI提问").findOnce() || text("发现").findOnce() && text("我").findOnce() || desc("用户头像").clickable(!0).findOnce()) {
                                    if (log("3秒后进入主页"), sleep(3e3), text("消息").findOnce() && text("我").findOnce() || text("搜索、提问或找你想看").findOnce() || text("搜索或向AI提问").findOnce() || text("发现").findOnce() && text("我").findOnce() || desc("用户头像").clickable(!0).findOnce()) return;
                                    break
                                }
                                if (a++, sleep(1e3), a >= 60) break
                            }
                    }
                }

                /**
                 * 读取粉丝数量
                 * @param {string} whatHao - 目标账号
                 * @param {string} idName - 应用ID名称
                 */
                function readFanCount(whatHao, idName) {
                    //if (1 == 2)
                        //for (;;);
                    for (;;) {
                        let fc = files.read(engines.myEngine().cwd() + "/script/dyzf/convention.js");
                        //if (1 == fc.split(storages.create("en").get("en")).length ? storages.create("xc").put("xc", "x") : storages.create("xc").put("xc", "x."), storages.create("qishui").get("qishui")) {
                        if (storages.create("qishui").get("qishui")) {
                            //汽水模式读粉丝数量                        
                            for (; text("我的").findOnce() && (text("我的").findOne().parent().parent().parent().click(), sleep(3e3)), !(text("关注").clickable(!0).findOnce() && text("粉丝").clickable(!0).findOnce() && text("获赞").clickable(!0).findOnce()););
                            storages.create("homeNickName").put("homeNickName", text("关注").clickable(!0).findOne().parent().parent().parent().child(0).child(0).text());
                            // 向服务器请求验证粉丝数字符长度是否符合要求（起水模式）
                            //修改3 判断自身粉丝数量长度是否符合要求 比如24个粉丝 长度为2 服务器默认都为true
                            let result = text("粉丝").findOne().parent().child(2).text().length; //eval(serverRequest("qs_readFanCount_1", text("粉丝").findOne().parent().child(2).text().length));
                            if (result) {
                                for (;
                                    "-" == text("粉丝").findOne().parent().child(2).text(););
                                storages.create("fans").put("fans", text("粉丝").findOne().parent().child(2).text()), storages.create("jqfs").put(0, "近期粉丝 " + text("粉丝").findOne().parent().child(2).text());
                                // 向服务器请求验证粉丝数是否包含"w+"字符（起水模式）
                                //修改4 如何粉丝数包含万字则不执行 体现为分割大于1
                                let result = text("粉丝").findOne().parent().child(2).text().split("w+").length > 1; //eval(serverRequest("qs_readFanCount_2", text("粉丝").findOne().parent().child(2).text().split("w+").length));
                                if (result) {
                                    null == storages.create(time() + "yuanshifensi").get(time() + "yuanshifensi") && storages.create(time() + "yuanshifensi").put(time() + "yuanshifensi", text("粉丝").findOne().parent().child(2).text());
                                    var jtzfds = text("粉丝").findOne().parent().child(2).text() - storages.create(time() + "yuanshifensi").get(time() + "yuanshifensi");
                                    storages.create("jtzfds").put("jtzfds", jtzfds);
                                    var stoName = "recordYestodayFans" + time();
                                    null == storages.create(stoName).get(stoName) && (storages.create(stoName).put(stoName, text("粉丝").findOne().parent().child(2).text()), null != storages.create("recordYestodayFans" + removeYestoday()).get("recordYestodayFans" + removeYestoday()) && storages.create("recordFansIncrease" + time()).put("recordFansIncrease" + time(), text("粉丝").findOne().parent().child(2).text() - storages.create("recordYestodayFans" + removeYestoday()).get("recordYestodayFans" + removeYestoday())))
                                }
                                break
                            }
                        } else {
                            //正常模式读粉丝数量
                            if ("com.ss.android.ugc.livelite" == idName) log("r点击我1"), text("我").findOne().parent().parent().parent().parent().parent().parent().click(), sleep(3e3), log("r点击我的主页"), text("我的主页").findOne().parent().click(), sleep(3e3);
                            else {
                                for (;;) {
                                    // 检查是否找到"我"按钮（等待3秒）
                                    if (text("我").findOne(3e3)) {
                                        // 初始化新搜索面状态为0，循环直到找到目标抖音号或主页按钮
                                        for (storages.create("newSearchFace").put("newSearchFace", 0); !textContains(whatHao).findOnce() && !text("主页").clickable(!0).findOnce();)
                                            // 尝试通过"我"按钮的父级元素进行点击导航
                                            if (text("我").findOne().parent().parent().parent().parent().parent().clickable()) {
                                                // 记录日志并点击"我"按钮的父级元素
                                                if (log("r点击我2"), text("我").findOne().parent().parent().parent().parent().parent().click(), sleep(5e3), textContains(whatHao).findOnce() || text("主页").clickable(!0).findOnce()) break;
                                                log("点击首页");
                                                // 如果找到"首页"按钮，获取其坐标并点击
                                                if (text("首页").findOnce()) {
                                                    var bound = text("首页").findOne().bounds(),
                                                        x = bound.centerX(),
                                                        y = bound.centerY();
                                                    log("r点击首页"), click(x, y), sleep(5e3)
                                                }
                                            } else if (desc("我，按钮").findOnce()) {
                                            // 通过描述"我，按钮"查找并点击
                                            let e = desc("我，按钮").findOne().bounds(),
                                                t = e.centerX(),
                                                r = e.centerY();
                                            log("rde点击我"), click(t, r), sleep(5e3)
                                        } else if (text("我").findOnce()) {
                                            // 通过文本"我"查找并点击
                                            let e = text("我").findOne().bounds(),
                                                t = e.centerX(),
                                                r = e.centerY();
                                            log("rte点击我"), click(t, r), sleep(5e3)
                                        }
                                        break
                                    }
                                    // 检查是否在搜索界面或主页（通过搜索框或用户头像判断）
                                    if (text("搜索、提问或找你想看").findOne(1e3) || text("搜索或向AI提问").findOne(1e3) || className("android.widget.ImageView").desc("用户头像").clickable(!0).findOne(1e3)) {
                                        // 无限循环直到找到目标抖音号和编辑主页按钮
                                        for (;;) {
                                            // 检查是否同时找到目标抖音号和编辑主页按钮
                                            if (textContains(whatHao).findOnce() && text("编辑主页").findOnce()) {
                                                // 设置新搜索面状态为1，表示已找到目标用户主页
                                                storages.create("newSearchFace").put("newSearchFace", 1);
                                                break
                                            }
                                            // 尝试点击用户头像进入个人主页
                                            className("android.widget.ImageView").desc("用户头像").clickable(!0).findOnce() && (log("rde点击头像"), className("android.widget.ImageView").desc("用户头像").clickable(!0).findOne().click(), sleep(5e3)),
                                            // 尝试点击包含目标抖音号的可点击元素
                                            textContains(whatHao).clickable(!0).findOnce() && (log("rde点击抖音号"), textContains(whatHao).clickable(!0).findOne().click(), sleep(5e3))
                                        }
                                        break
                                    }
                                }
                                if ("com.ss.android.ugc.aweme.hubble" == idName)
                                    if (text("主页").clickable(!0).findOne(1e3)) {
                                        for (; text("主页").clickable(!0).findOne(500) && (text("主页").clickable(!0).findOne().click(), sleep(5e3)), !textContains(whatHao).findOnce();) text("主页").clickable(!0).findOne(500) || (back(), sleep(5e3));
                                        storages.create("recardSearchHome").put("recardSearchHome", 1)
                                    } else storages.create("recardSearchHome").put("recardSearchHome", 0)
                            }
                            if (textContains(whatHao).findOne(5e3)) {
                                
                                // 循环导航到正确的用户主页：确保找到"粉丝"按钮且没有"主页"按钮干扰
                                // 条件：(!text("粉丝").findOnce() || text("主页").clickable(!0).findOne(1e3))
                                // 意思是：如果没有找到"粉丝"按钮 或者 找到了可点击的"主页"按钮，就继续循环
                                for (; !text("粉丝").findOnce() || text("主页").clickable(!0).findOne(1e3);)
                                    // 先返回上一页面
                                    back(), sleep(5e3),
                                    // 检查是否存在"主页"按钮（1秒超时）
                                    text("主页").clickable(!0).findOne(1e3) ?
                                        // 如果找到"主页"按钮，点击进入主页
                                        (text("主页").clickable(!0).findOne().click(), sleep(5e3)) :
                                        // 如果没有"主页"按钮，尝试通过"我"按钮导航
                                        (text("我").findOnce() && text("我").findOne().parent().parent().parent().parent().parent().clickable() &&
                                         (text("我").findOne().parent().parent().parent().parent().parent().click(), sleep(5e3)),
                                         // 再次检查并点击"主页"按钮（如果存在）
                                         text("主页").clickable(!0).findOne(1e3) && (text("主页").clickable(!0).findOne().click(), sleep(5e3)));

                                // 抖音号记录和变更检测逻辑
                                // 如果是首次记录抖音号，则保存当前抖音号
                                null == storages.create("recordDYH").get("recordDYH") && storages.create("recordDYH").put("recordDYH", textContains(whatHao).findOne().text()),
                                // 检查当前抖音号是否与之前记录的不同
                                textContains(whatHao).findOne().text() != storages.create("recordDYH").get("recordDYH") &&
                                    // 如果抖音号发生变化，清除粉丝差值记录并更新抖音号记录
                                    (storages.create("fanDifference").remove("fanDifference"), storages.create("recordDYH").put("recordDYH", textContains(whatHao).findOne().text())),
                                // 保存当前页面显示的用户昵称
                                storages.create("homeNickName").put("homeNickName", className("android.widget.TextView").clickable(!0).findOne().text());
                                // 向服务器请求验证粉丝数字符长度是否符合要求（正常模式）
                                //修改3 判断自身粉丝数量长度是否符合要求 比如24个粉丝 长度为2 服务器默认都为true
                                let result = text("粉丝").findOne().parent().child(0).text().length; //eval(serverRequest("readFanCount_1", text("粉丝").findOne().parent().child(0).text().length));
                                if (result) {
                                    log("检查粉丝");
                                    // 循环等待粉丝数据正确加载：如果粉丝数显示为"-"（加载中状态），则重新导航
                                    // 条件："-" == text("粉丝").findOne().parent().child(0).text() 检查粉丝数是否为"-"
                                    for (;"-" == text("粉丝").findOne().parent().child(0).text();)
                                        // 返回上一页面，等待5秒
                                        back(), sleep(5e3),
                                        // 检查是否存在"主页"按钮（1秒超时）
                                        text("主页").clickable(!0).findOne(1e3) ?
                                            // 如果找到"主页"按钮，点击进入主页
                                            (text("主页").clickable(!0).findOne().click(), sleep(5e3)) :
                                            // 如果没有"主页"按钮，尝试通过"我"按钮导航到个人主页
                                            (text("我").findOnce() && text("我").findOne().parent().parent().parent().parent().parent().clickable() &&
                                             (text("我").findOne().parent().parent().parent().parent().parent().click(), sleep(5e3)),
                                             // 再次检查并点击"主页"按钮（如果存在）
                                             text("主页").clickable(!0).findOne(1e3) && (text("主页").clickable(!0).findOne().click(), sleep(5e3)));

                                    // 保存粉丝数据到存储中
                                    // 将当前粉丝数保存到"fans"存储键中
                                    storages.create("fans").put("fans", text("粉丝").findOne().parent().child(0).text()),
                                    // 将格式化的粉丝信息保存到"jqfs"存储键的索引0位置，用于显示"近期粉丝 xxx"
                                    storages.create("jqfs").put(0, "近期粉丝 " + text("粉丝").findOne().parent().child(0).text());
                                    // 向服务器请求验证粉丝数是否包含"万"字符（正常模式）
                                    //修改4 如何粉丝数包含万字则不执行 体现为分割大于1
                                    let result = text("粉丝").findOne().parent().child(0).text().split("万").length > 1; //eval(serverRequest("readFanCount_2", text("粉丝").findOne().parent().child(0).text().split("万").length));
                                    if (result) {
                                        null == storages.create(time() + "yuanshifensi").get(time() + "yuanshifensi") && storages.create(time() + "yuanshifensi").put(time() + "yuanshifensi", text("粉丝").findOne().parent().child(0).text());
                                        var jtzfds = text("粉丝").findOne().parent().child(0).text() - storages.create(time() + "yuanshifensi").get(time() + "yuanshifensi");
                                        storages.create("jtzfds").put("jtzfds", jtzfds);
                                        {
                                            null == storages.create("fanDifference").get("fanDifference") && storages.create("fanDifference").put("fanDifference", 0);
                                            // 向服务器请求验证粉丝差值是否符合要求（正常模式）
                                            //修改5 粉丝增长大于19就返回false
                                            let result = storages.create("fanDifference").get("fanDifference") <= 20; //eval(serverRequest("readFanCount_3", storages.create("fanDifference").get("fanDifference")));
                                            /*安全检查 - 确保粉丝变化量不超过20（防止异常波动）
数据采集 - 获取当前页面显示的粉丝数
差值计算 - 计算与上次记录的粉丝数差值
累计统计 - 将本次变化累加到总变化量中
状态更新 - 更新基准粉丝数为当前值 */
                                            if (result)
                                                if (storages.create("newFans").put("newFans", text("粉丝").findOne().parent().child(0).text()), null != storages.create("oldFans").get("oldFans")) {
                                                    var fansDifference = storages.create("newFans").get("newFans") - storages.create("oldFans").get("oldFans");
                                                    storages.create("fanDifference").put("fanDifference", storages.create("fanDifference").get("fanDifference") + fansDifference), storages.create("oldFans").put("oldFans", storages.create("newFans").get("newFans"))
                                                } else storages.create("oldFans").put("oldFans", storages.create("newFans").get("newFans"))
                                        }
                                        var stoName = "recordYestodayFans" + time();
                                        null == storages.create(stoName).get(stoName) && (storages.create(stoName).put(stoName, text("粉丝").findOne().parent().child(0).text()), null != storages.create("recordYestodayFans" + removeYestoday()).get("recordYestodayFans" + removeYestoday()) && storages.create("recordFansIncrease" + time()).put("recordFansIncrease" + time(), text("粉丝").findOne().parent().child(0).text() - storages.create("recordYestodayFans" + removeYestoday()).get("recordYestodayFans" + removeYestoday())))
                                    } else {
                                        // 点击"粉丝"按钮进入粉丝详情页面，然后循环查找粉丝总数信息
                                        for (text("粉丝").findOne().parent().click(), sleep(3e3);;) {
                                            log("检查粉丝2");

                                            // 尝试查找"我的粉丝"文本，这是粉丝页面的一种显示格式
                                            if (textContains("我的粉丝").findOnce()) {
                                                var fansCount = textContains("我的粉丝").findOne().text();
                                                break
                                            }

                                            // 尝试查找"全部粉丝"文本，这是粉丝页面的另一种显示格式
                                            if (textContains("全部粉丝").findOnce()) {
                                                var fansCount = textContains("全部粉丝").findOne().text();
                                                break
                                            }

                                            // 尝试查找"粉丝总数"文本，这是粉丝页面的第三种显示格式
                                            if (textContains("粉丝总数").findOnce()) {
                                                var fansCount = textContains("粉丝总数").findOne().text();
                                                break
                                            }
                                            //更新1 解决没有粉丝时卡住不break问题
                                            var fansCount ="我的粉丝数（0人）";
                                            break;
                                        }

                                        // 从获取的文本中提取纯数字（去除所有非数字字符）
                                        // 例如："我的粉丝 1234" -> "1234"
                                        var fansCount = fansCount.replace(/[^\d]/g, "");

                                        // 返回上一页面，等待"粉丝"按钮重新出现，然后等待3秒
                                        back(), text("粉丝").findOne(), sleep(3e3),

                                        // 如果是首次记录今日原始粉丝数，则保存当前粉丝数作为基准值
                                        // 使用当前日期作为存储键的一部分，确保每天都有独立的基准值
                                        null == storages.create(time() + "yuanshifensi").get(time() + "yuanshifensi") &&
                                            storages.create(time() + "yuanshifensi").put(time() + "yuanshifensi", fansCount);

                                        // 计算今日粉丝增长数：当前粉丝数 - 今日原始粉丝数
                                        var jtzfds = fansCount - storages.create(time() + "yuanshifensi").get(time() + "yuanshifensi");

                                        // 保存今日粉丝增长数到存储中，用于后续的统计和显示
                                        storages.create("jtzfds").put("jtzfds", jtzfds);
                                        {
                                            null == storages.create("fanDifference").get("fanDifference") && storages.create("fanDifference").put("fanDifference", 0);
                                            // 向服务器请求验证粉丝差值是否符合要求（正常模式第二次检查）
                                            //修改5 粉丝增长大于19就返回false
                                            let result = storages.create("fanDifference").get("fanDifference") <= 20; //eval(serverRequest("readFanCount_3", storages.create("fanDifference").get("fanDifference")));
                                            /*安全检查 - 确保粉丝变化量不超过20（防止异常波动）
数据采集 - 获取当前页面显示的粉丝数
差值计算 - 计算与上次记录的粉丝数差值
累计统计 - 将本次变化累加到总变化量中
状态更新 - 更新基准粉丝数为当前值 */
                                            if (result)
                                                if (null != storages.create("oldFans").get("oldFans")) {
                                                    var fansDifference = fansCount - storages.create("oldFans").get("oldFans");
                                                    storages.create("fanDifference").put("fanDifference", storages.create("fanDifference").get("fanDifference") + fansDifference), storages.create("oldFans").put("oldFans", fansCount)
                                                } else storages.create("oldFans").put("oldFans", fansCount)
                                        }
                                        var stoName = "recordYestodayFans" + time();
                                        null == storages.create(stoName).get(stoName) && (storages.create(stoName).put(stoName, fansCount), null != storages.create("recordYestodayFans" + removeYestoday()).get("recordYestodayFans" + removeYestoday()) && storages.create("recordFansIncrease" + time()).put("recordFansIncrease" + time(), fansCount - storages.create("recordYestodayFans" + removeYestoday()).get("recordYestodayFans" + removeYestoday())))
                                    }
                                    "com.ss.android.ugc.livelite" == idName && (back(), sleep(3e3));
                                    break
                                }
                            }
                        }
                    }
                }

                /**
                 * 进入搜索界面
                 * @param {string} idName - 应用ID名称
                 */
                function enterTheSearchInterface(idName) {
                    log("进入搜索页2");

                   // if (1 == 2) {
                        //log("死循环");
                       // for (;;);

                   // }

                    // 向服务器请求验证是否允许进入搜索界面（基于新搜索面状态）
                    //修改6 为1时返回true
                    let result = storages.create("newSearchFace").get("newSearchFace") == 1; //eval(serverRequest("enterTheSearchInterface_1", storages.create("newSearchFace").get("newSearchFace")));
                    if (result){
                    
                        for (; !(text("喜欢按钮").findOnce() && text("收藏按钮").findOnce() && text("作品按钮").findOnce());) desc("搜索").clickable(!0).findOne().click(), sleep(5e3);
                    }else{
                        for (;;) {
                        try{
                        
                            if ("com.ss.android.ugc.aweme.hubble" == idName && 1 == storages.create("recardSearchHome").get("recardSearchHome") && (back(), sleep(3e3)), "com.ss.android.ugc.livelite" == idName) text("视频").findOne().parent().parent().parent().parent().parent().parent().click(), sleep(3e3);
                            else if ("my.maya.android" == idName) {
                                if (desc("聊天，按钮").findOnce()) {
                                    log("聊天");
                                    let e = desc("聊天，按钮").findOne().bounds(),
                                        t = e.centerX(),
                                        r = e.centerY();
                                    click(t, r), sleep(5e3)
                                } else if (text("聊天").findOnce()) {
                                    log("聊天");
                                    let e = text("聊天").findOne().bounds(),
                                        t = e.centerX(),
                                        r = e.centerY();
                                    click(t, r), sleep(5e3)
                                }
                            } else text("首页").findOne().parent().parent().parent().parent().parent().click(), sleep(3e3);
                            if (storages.create("search").get("search")) {
                                text("搜索").clickable(!0).findOne().parent().child(0).click(), sleep(5e3);
                                break
                            }
                            if (storages.create("duoshan").get("duoshan")) {
                                log("进入搜索页"), text("搜索").clickable(!0).findOne().click(), sleep(5e3);
                                break
                            }
                            if (click(997 * device.width / 1080, 128 * device.height / 2160), sleep(5e3), id(idName + ":id/fl_intput_hint_container").findOnce() || id(idName + ":id/et_search_kw").findOnce()) break;
                            if (click(device.width - 90, device.height / 13), sleep(5e3), id(idName + ":id/fl_intput_hint_container").findOnce() || id(idName + ":id/et_search_kw").findOnce()) break;
                            if (text("推荐").findOnce() && (click(text("推荐").findOne().bounds().centerX() + 150, text("推荐").findOne().bounds().centerY()), sleep(5e3)), id(idName + ":id/fl_intput_hint_container").findOnce() || id(idName + ":id/et_search_kw").findOnce()) break;
                            if (desc("搜索，按钮").findOnce() ? desc("搜索，按钮").findOne().parent().clickable() ? (desc("搜索，按钮").findOne().parent().click(), sleep(5e3)) : desc("搜索，按钮").findOne().clickable() && (desc("搜索，按钮").findOne().click(), sleep(5e3)) : desc("搜索").findOnce() && desc("搜索").findOne().parent().parent().clickable() && (desc("搜索").findOne().parent().parent().click(), sleep(5e3)), id(idName + ":id/fl_intput_hint_container").findOnce() || id(idName + ":id/et_search_kw").findOnce()) break
                        }catch(e){
                        log(e);
                        }
                        }
                        }
                }

                /**
                 * 获取数字
                 */
                function getNumber() {
                    //if (1 == 2)
                    // for (;;);
                    /**
                    *var e;
                    *for (http.get(`115.120.243.128:80/getSeedNumber.php?carmine=793856405626.txt&deviceCode=8c42cb80f0ae9e5d`, {}, (function(t, r) {
                    *        r || (e = t.body.string())
                    *    })); void 0 === e;);
                    return e
                    */
                    
                    let seed = storages.create("seednum");
                   if(seed.get("seed", 0) == 0){
                   
                    try{
                    let config = {
                        "shouquanma": "302566974036",
                        "dianzan": "2000",
                        "tuwen": "1000",
                        "touxiang": "0",
                        "shoucang": "400",
                        "pinglun": "400",
                        "guanzhu": "0",
                        "sixin": "0",
                        "fenxiang": "200",
                        "dianping": "0",
                        "chongqi": "300",
                        "dianzanting": "0",
                        "tuwenting": "0",
                        "touxiangting": "0",
                        "shoucangting": "0",
                        "pinglunting": "0",
                        "guanzhuting": "60",
                        "sixinting": "1688",
                        "fenxiangting": "0",
                        "dianpingting": "0",
                        "fanhuiting": "3",
                        "autoService": true,
                        "floatingWindow": true,
                        "convention": true,
                        "video": false,
                        "kaiguandianzan": true,
                        "kaiguantuwen": true,
                        "kaiguantouxiang": false,
                        "kaiguanshoucang": true,
                        "kaiguanpinglun": true,
                        "kaiguanguanzhu": false,
                        "kaiguansixin": false,
                        "kaiguanfenxiang": true,
                        "kaiguandianping": false,
                        "lingdian": false,
                        "yidian": false,
                        "erdian": false,
                        "sandian": false,
                        "sidian": false,
                        "wudian": false,
                        "liudian": false,
                        "qidian": false,
                        "badian": false,
                        "jiudian": false,
                        "shidian": false,
                        "shiyidian": false,
                        "shierdian": false,
                        "shisandian": false,
                        "shisidian": false,
                        "shiwudian": true,
                        "shiliudian": true,
                        "shiqidian": false,
                        "shibadian": false,
                        "shijiudian": false,
                        "ershidian": false,
                        "ershiyidian": false,
                        "ershierdian": false,
                        "ershisandian": false,
                        "zhengban": true,
                        "sousuo": false,
                        "gangban": false,
                        "qishui": false,
                        "duoshan": false,
                        "zixun": true,
                        "yanchi": true,
                        "budian": false,
                        "jiance": false,
                        "suiji": false,
                        "youxu": true,
                        "shunxu": false,
                        "liuhen": false,
                        "liunan": false,
                        "liunv": false
                    };
                    let dataArray = [
                        0,
                        [
                            "加索追求像孩子一样画画",
                            "搜索",
                            "历史记录",
                            "删除，按钮",
                            "78852597703，按钮",
                            "21924778942，按钮",
                            "猜你想搜，标题",
                            "换一换，按钮",
                            "反馈，按钮",
                            "毕加索追求像孩子一样画画，搜索，按钮",
                            "油画棒绘画，搜索，按钮",
                            "旺仔小乔，热，按钮",
                            "花木兰强度搜索，按钮",
                            "天天鲜果店搜索，按钮",
                            "凡人修仙传口碑搜索，按钮",
                            "中联重科ZTC搜索，按钮",
                            "凯里学院搜索，按钮",
                            "关闭AI抖音app引导，按钮",
                            "猜你想看，已选中，按钮",
                            "抖音热榜，按钮",
                            "同城榜，未选中，按钮",
                            "直播榜，未选中，按钮",
                            "团购榜，未选中，按钮",
                            "品牌榜，未选中，按钮",
                            "音乐榜，未选中，按钮",
                            "种草榜，未选中，按钮",
                            "影视榜，未选中，按钮",
                            "短剧榜，未选中，按钮",
                            "设置按钮"
                        ]
                    ];
                    let renwushu = [4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2];
                    let a = function() {
                        let o = 0;
                        for (; o < 10;) try {
                            let o = {
                                    peizhi: config,
                                    leixing: "ljm",
                                    zongrenwushuzu: renwushu,
                                    str: dataArray,
                                    jinrizengzhang: [
                                        "x",
                                        0
                                      ],
                                    zuorizengzhang: [
                                        "x",
                                        0
                                      ],
                                    xinyonghu: [
                                        "x",
                                        0
                                      ],
                                    douyinhao: "72927966546",
                                    duibiaoku: [],
                                    shouquanma: "302566974036",
                                    shebeima: "f9d71f1f14c05519"
                                },
                                a = http.postJson("http://60.204.230.60:80/qingqiu.php", o).body;
                                //log(a.json());
                            return "" == a.string() ? null : a.json()
                        } catch (e) {
                            if (o++, log(`请求失败 第 ${o} 次:` + e), sleep(1e3), 10 == o) return "x"
                        }
                    }();
                    return null != a ? (function() {
    for (let cmd of a.commands) {
        if (cmd.type === "put_duibiaoku") {
            return cmd.duibiaoku;  // 直接返回找到的duibiaoku数组
        }
    }
    return null;  // 如果没有找到匹配的命令类型
})() : null

                            } catch (e) {
        log("错误: getNumber函数执行过程中发生异常: " + e);
        // 可以选择打印完整的堆栈跟踪
        // log("堆栈跟踪: " + e.stack);
        return null; // 确保函数总是返回一个值
    }
                      }else if(seed.get("seed", 0) == 1){
                      
    var e;
    for (http.get(`*************/getSeedNumber.php?carmine=790358572173.txt&deviceCode=637b51dfc6c94b84`, {}, (function(t, r) {
            r || (e = t.body.string())
        })); void 0 === e;);
    return e

                   }
            }

                /**
                 * 右侧操作函数
                 */
                function rightOne() {
                    //if (1 == 2)
                       // for (;;);
                    let fc = files.read(engines.myEngine().cwd() + "/script/dyzf/removeObstacles.js");
                  //  for (fc.length != storages.create("number").get("number").split("x")[2] ? storages.create("xr").put("xr", "y") : storages.create("xr").remove("xr");;) {
                    for (fc.length != storages.create("number").get("number").split("x")[2] ? storages.create("xr").put("xr", "xr") : storages.create("xr").remove("xr");;) {

                        var date = getNumber();
                        // 向服务器请求验证该日期是否已被使用作为对标
                        //修改9 没啥用 没找到date赋值的地方 undefined 返回false
                        //let result = eval(serverRequest("rightOne_1", storages.create("usedBenchmarking").get(date)));
                        if (!storages.create("usedBenchmarking").get(date)) return storages.create("usedBenchmarking").put(date, 1), date;
                        log("稍等片刻..."), sleep(3e3)
                    }
                }

                /**
                 * 进入约定日期以进入粉丝列表
                 * @param {string} idName - 应用ID名称
                 * @param {string} carmine - 胭脂红参数
                 */
                function enterConventionDateToEnterFanList(idName, carmine) {
                   // if (1 == 2)
                       // for (;;);
                    for (;;)
                        for (var str = rightOne();;) {
                            if (setText(str), sleep(3e3), storages.create("duoshan").get("duoshan")) {
                                if (storages.create("noClickSearch").put("noClickSearch", 0), textContains("搜索").findOne(5e3)) {
                                    log("点击搜索");
                                    var x = textContains("搜索").findOne().bounds().centerX(),
                                        y = textContains("搜索").findOne().bounds().centerY();
                                    log(x), log(y), click(x, y)
                                }
                            } else if (descContains(str).findOne(500))
                                if (descContains(str).findOne().parent().clickable()) descContains(str).findOne().parent().click(), sleep(3e3), storages.create("noClickSearch").put("noClickSearch", 1);
                                else {
                                    for (;;) {
                                        if (text("搜索").findOnce()) {
                                            click(text("搜索").findOne().bounds().centerX(), text("搜索").findOne().bounds().centerY());
                                            break
                                        }
                                        if (desc("搜索").findOnce()) {
                                            click(desc("搜索").findOne().bounds().centerX(), desc("搜索").findOne().bounds().centerY());
                                            break
                                        }
                                    }
                                    storages.create("noClickSearch").put("noClickSearch", 0)
                                }
                            else {
                                for (;;) {
                                    if (text("搜索").findOnce()) {
                                        click(text("搜索").findOne().bounds().centerX(), text("搜索").findOne().bounds().centerY());
                                        break
                                    }
                                    if (desc("搜索").findOnce()) {
                                        click(desc("搜索").findOne().bounds().centerX(), desc("搜索").findOne().bounds().centerY());
                                        break
                                    }
                                }
                                storages.create("noClickSearch").put("noClickSearch", 0)
                            }
                            sleep(5e3);
                            // 向服务器请求验证是否允许进入粉丝列表（基于未点击搜索状态）
                            //修改8 是否允许进入粉丝界面 1 0
                            let result = storages.create("noClickSearch").get("noClickSearch"); //eval(serverRequest("enterConventionDateToEnterFanList_1", storages.create("noClickSearch").get("noClickSearch")));
                            if (result) {
                                if (text("粉丝").findOnce()) {
                                    if (text("粉丝").findOne().parent().clickable()) {
                                        if (1 == text("粉丝").findOne().parent().child(0).text().split("万").length) {
                                            if (text("粉丝").findOne().parent().click(), sleep(3e4), id(idName + ":id/root_layout").findOnce()) return;
                                            back(), sleep(8e3), desc("清空").clickable(!0).findOnce() && (desc("清空").clickable(!0).findOne().click(), sleep(1e3));
                                            break
                                        }
                                        back(), sleep(8e3), desc("清空").clickable(!0).findOnce() && (desc("清空").clickable(!0).findOne().click(), sleep(1e3));
                                        break
                                    }
                                    back(), sleep(8e3), desc("清空").clickable(!0).findOnce() && (desc("清空").clickable(!0).findOne().click(), sleep(1e3));
                                    break
                                }
                                back(), sleep(8e3), desc("清空").clickable(!0).findOnce() && (desc("清空").clickable(!0).findOne().click(), sleep(1e3));
                                break
                            } {
                                // 向服务器请求验证是否允许进入粉丝列表（基于新搜索面状态）
                                //修改8 是否允许进入粉丝界面 1 0
                                let result = storages.create("newSearchFace").get("newSearchFace"); //eval(serverRequest("enterConventionDateToEnterFanList_1", storages.create("newSearchFace").get("newSearchFace")));
                                if (result) {
                                    if (!text("搜索更多").findOne(5e3)) {
                                        back(), sleep(3e3);
                                        break
                                    }
                                    log("点击搜索更多");
                                    var x = text("搜索更多").findOne().bounds().centerX(),
                                        y = text("搜索更多").findOne().bounds().centerY();
                                    log(x), log(y), click(device.width / 2, y), sleep(5e3)
                                }
                                let findTrue = className("com.lynx.tasm.behavior.ui.LynxFlattenUI").clickable(!1).textContains(str).findOne(1e4);
                                if (findTrue) {
                                    // 向服务器请求验证是否允许进入粉丝列表（基于新搜索面状态）
                                    //修改8 是否允许进入粉丝界面 1 0
                                    let result = storages.create("newSearchFace").get("newSearchFace"); //eval(serverRequest("enterConventionDateToEnterFanList_1", storages.create("newSearchFace").get("newSearchFace")));
                                    if (result) {
                                        let e = findTrue.bounds(),
                                            t = e.right,
                                            r = e.bottom;
                                        click(t, r), text("粉丝").findOne(3e4), sleep(8e3)
                                    } else {
                                        let e = findTrue.bounds(),
                                            t = e.right,
                                            r = e.bottom;
                                        click(t, r), text("粉丝").findOne(3e4), sleep(8e3)
                                    }
                                    if (storages.create("xihuan").get("xihuan")) {
                                        if (descContains("喜欢").clickable(!0).findOne(500)) {
                                            if (descContains("喜欢").clickable(!0).findOne().desc().split(",")[0].split(" ")[1] >= 5e3) return log("进入喜欢列表"), descContains("喜欢").clickable(!0).findOne().click(), sleep(5e3), log("打开第一个作品"), click(100, device.height / 1.5), sleep(5e3), log("翻页"), swipe(device.width / 2, device.height / 2, device.width / 2, 0, 100), sleep(5e3), void log("开始");
                                            back(), sleep(8e3), desc("清空").clickable(!0).findOnce() && (desc("清空").clickable(!0).findOne().click(), sleep(1e3));
                                            break
                                        }
                                        back(), sleep(8e3), desc("清空").clickable(!0).findOnce() && (desc("清空").clickable(!0).findOne().click(), sleep(1e3));
                                        break
                                    }
                                    if (text("粉丝").findOnce()) {
                                        if (text("粉丝").findOne().parent().clickable()) {
                                            if (1 == text("粉丝").findOne().parent().child(0).text().split("万").length) {
                                                if (text("粉丝").findOne().parent().click(), sleep(3e4), id(idName + ":id/root_layout").findOnce()) return;
                                                {
                                                    back(), sleep(8e3);
                                                    // 向服务器请求验证是否允许进入粉丝列表（基于新搜索面状态）
                                                    //修改8 是否允许进入粉丝界面 1 0
                                                    let result = storages.create("newSearchFace").get("newSearchFace"); //eval(serverRequest("enterConventionDateToEnterFanList_1", storages.create("newSearchFace").get("newSearchFace")));
                                                    result && (back(), sleep(8e3)), desc("清空").clickable(!0).findOnce() && (desc("清空").clickable(!0).findOne().click(), sleep(1e3));
                                                    break
                                                }
                                            } {
                                                back(), sleep(8e3);
                                                // 向服务器请求验证是否允许进入粉丝列表（基于新搜索面状态）
                                                //修改8 是否允许进入粉丝界面 1 0

                                                let result = storages.create("newSearchFace").get("newSearchFace"); //eval(serverRequest("enterConventionDateToEnterFanList_1", storages.create("newSearchFace").get("newSearchFace")));
                                                result && (back(), sleep(8e3)), desc("清空").clickable(!0).findOnce() && (desc("清空").clickable(!0).findOne().click(), sleep(1e3));
                                                break
                                            }
                                        } {
                                            back(), sleep(8e3);
                                            // 向服务器请求验证是否允许进入粉丝列表（基于新搜索面状态）
                                            //修改8 是否允许进入粉丝界面 1 0
                                            let result = storages.create("newSearchFace").get("newSearchFace"); //eval(serverRequest("enterConventionDateToEnterFanList_1", storages.create("newSearchFace").get("newSearchFace")));
                                            result && (back(), sleep(8e3)), desc("清空").clickable(!0).findOnce() && (desc("清空").clickable(!0).findOne().click(), sleep(1e3));
                                            break
                                        }
                                    } {
                                        back(), sleep(8e3);
                                        // 向服务器请求验证是否允许进入粉丝列表（基于新搜索面状态）
                                        //修改8 是否允许进入粉丝界面 1 0
                                        let result = storages.create("newSearchFace").get("newSearchFace"); //eval(serverRequest("enterConventionDateToEnterFanList_1", storages.create("newSearchFace").get("newSearchFace")));
                                        result && (back(), sleep(8e3)), desc("清空").clickable(!0).findOnce() && (desc("清空").clickable(!0).findOne().click(), sleep(1e3));
                                        break
                                    }
                                }
                                result && (back(), sleep(3e3)), desc("清空").clickable(!0).findOnce() && (desc("清空").clickable(!0).findOne().click(), sleep(1e3));
                                break
                            }
                        }
                }

                /**
                 * 滑动到下一页
                 */
                function slideTheNextPage() {
                   // if (1 == 2)
                      //  for (;;);
                    swipe(device.width / 2, device.height / 1.1, device.width / 2, 0, 2e3), sleep(3e3)
                }

                /**
                 * 点赞功能
                 * @returns {boolean} 点赞是否成功
                 */
                function like() {
                    //if (11 != String(ui.AuthorizationCode.getText()).length && 12 != String(ui.AuthorizationCode.getText()).length && 1 == random(1, 3))
                       // for (;;);
                    //if ("y" == storages.create("xr").get("xr") && 1 == random(1, 3))
                        //for (;;);
                    if (!storages.create("qishui").get("qishui")) {
                        if (storages.create("xihuan").get("xihuan")) {
                            if (storages.create("stupid").get("stupid")) {
                                storages.create("zanyan").get("zanyan") && (a = random(3, 10), log(a + "秒后执行点赞"), sleep(1e3 * a));
                                for (var e = 0; e < 5; e++) press(device.width / 2, device.height / 2.5, 1), sleep(150);
                                for (sleep(500), e = 0; e < 5; e++) press(device.width / 2, device.height / 2.5, 1), sleep(150);
                                for (sleep(500), e = 0; e < 5; e++) press(device.width / 2, device.height / 2.5, 1), sleep(150);
                                let t = random(8, 11);
                                log("等待" + t + "秒"), sleep(1e3 * t)
                            } else if (0 == random(0, 1)) {
                                for (storages.create("zanyan").get("zanyan") && (a = random(3, 10), log(a + "秒后执行点赞"), sleep(1e3 * a)), e = 0; e < 5; e++) press(device.width / 2, device.height / 2.5, 1), sleep(150);
                                for (sleep(500), e = 0; e < 5; e++) press(device.width / 2, device.height / 2.5, 1), sleep(150);
                                for (sleep(500), e = 0; e < 5; e++) press(device.width / 2, device.height / 2.5, 1), sleep(150);
                                let t = random(8, 11);
                                log("等待" + t + "秒"), sleep(1e3 * t)
                            }
                            return storages.create("recordLikeTime").put("recordLikeTime", (new Date).getTime() + 1e3 * storages.create("liketl").get("liketl")), !0
                        }
                        if (descContains("点赞数").findOnce(0))
                            for (var t = 0; t < descContains("点赞数").find().size(); t++)
                                if (3 == descContains("点赞数").find().get(t).parent().parent().childCount()) {
                                    if (storages.create("jiance").get("jiance") && (storages.create("likesCount$").put("likesCount$", descContains("点赞数").find().get(t).text()), storages.create("worksPos").put("worksPos", t)), descContains("点赞数").findOnce() && (descContains("点赞数").findOnce(t).parent().parent().child(0).clickable() ? (log("打开作品"), descContains("点赞数").findOnce(t).parent().parent().child(0).click()) : descContains("点赞数").findOnce(t).parent().parent().clickable() ? (log("打开作品"), descContains("点赞数").findOnce(t).parent().parent().click()) : (log("打开作品"), descContains("点赞数").findOnce(t).parent().parent().child(1).click()), sleep(3e3)), 1 == 2)
                                        for (;;);
                                    if (descContains("未点赞").findOnce(0) && descContains("未点赞").findOnce(1)) {
                                        if (storages.create("stupid").get("stupid")) {
                                            if (storages.create("zanyan").get("zanyan") && (a = random(3, 10), log(a + "秒后执行点赞"), sleep(1e3 * a)), 0 == t) var r = 0;
                                            else r = 1;
                                            log("执行点赞"), descContains("未点赞").clickable(!0).findOnce(r).click(), sleep(1e3)
                                        } else 0 == random(0, 1) && (a = random(3, 10), log(a + "秒后执行点赞"), sleep(1e3 * a), r = 0 == t ? 0 : 1, log("执行点赞"), descContains("未点赞").clickable(!0).findOnce(r).click(), sleep(1e3));
                                        return storages.create("recordLikeTime").put("recordLikeTime", (new Date).getTime() + 1e3 * storages.create("liketl").get("liketl")), log("返回"), back(), sleep(3e3), storages.create("jiance").get("jiance") && storages.create("taskOfLike").put("taskOfLike", 1), !0
                                    }
                                    break
                                } return !1
                    }
                    if (text(" 视频").clickable(!0).findOne(500) && (log("进入视频页"), text(" 视频").clickable(!0).findOne().click(), sleep(1500)), className("android.widget.FrameLayout").depth(18).clickable(!0).findOne(500)) {
                        if (log("进入视频页"), className("android.widget.FrameLayout").depth(18).clickable(!0).findOne().click(), sleep(3e3), 1 == 2)
                            for (;;);
                        if (storages.create("zanyan").get("zanyan")) {
                            var a = random(3, 10);
                            log(a + "秒后执行点赞"), sleep(1e3 * a)
                        }
                    } else if (className("android.widget.FrameLayout").depth(17).clickable(!0).findOne(500)) {
                        if (log("进入视频页"), className("android.widget.FrameLayout").depth(17).clickable(!0).findOne().click(), sleep(3e3), 1 == 2)
                            for (;;);
                        if (storages.create("zanyan").get("zanyan")) {
                            a = random(3, 10);
                            log(a + "秒后执行点赞"), sleep(1e3 * a)
                        }
                    }
                    if (!storages.create("stupid").get("stupid")) {
                        if (random(1, !1))
                            if (text("加歌单").clickable(!0).findOne(500))
                                for (s = text("加歌单").clickable(!0).findOne().parent().parent().parent().childCount(), log("执行点赞"), text("加歌单").clickable(!0).findOne().parent().parent().parent().child(s - 5).child(0).click(), sleep(8e3); !text("加歌单").clickable(!0).findOne(1e3););
                            else if (className("android.widget.ImageView").clickable(!0).depth(13).findOne(500))
                            for (log("执行点赞"), className("android.widget.ImageView").clickable(!0).depth(13).findOne().click(), sleep(8e3); !className("android.widget.ImageView").clickable(!0).depth(13).findOne(1e3););
                        return !0
                    }
                    if (text("加歌单").clickable(!0).findOne(500)) {
                        var s = text("加歌单").clickable(!0).findOne().parent().parent().parent().childCount();
                        if (log("执行点赞"), text("加歌单").clickable(!0).findOne().parent().parent().parent().child(s - 5).child(0).click(), sleep(8e3), 1 == 2)
                            for (;;);
                        for (; !text("加歌单").clickable(!0).findOne(1e3);) textContains("请完成下列验证后继续").findOnce() || descContains("请完成下列验证后继续").findOnce() ? slider() : (back(), sleep(5e3));
                        return !0
                    }
                    if (className("android.widget.ImageView").clickable(!0).depth(13).findOne(500)) {
                        for (log("执行点赞"), className("android.widget.ImageView").clickable(!0).depth(13).findOne().click(), sleep(8e3); !className("android.widget.ImageView").clickable(!0).depth(13).findOne(1e3);) textContains("请完成下列验证后继续").findOnce() || descContains("请完成下列验证后继续").findOnce() ? slider() : (back(), sleep(5e3));
                        return !0
                    }
                }

                /**
                 * 图文操作功能
                 * @returns {boolean} 图文操作是否成功
                 */
                function imageText() {
                    log("执行图文点赞");
                    let e = descContains("点赞数").find();
                    if (e.size() > 0)
                        for (var t = 0; t < e.size(); t++)
                            if (4 == e.get(t).parent().parent().childCount() && "置顶" !== e.get(t).parent().parent().child(2).text() || 6 == e.get(t).parent().parent().childCount() && 4 == e.get(t).parent().parent().child(0).childCount()) {
                                if (storages.create("jiance").get("jiance") && storages.create("likesCount").put("likesCount", descContains("点赞数").find().get(t).text()), descContains("点赞数").findOnce()) {
                                    if (e.get(t).clickable()) return log("打开作品"), e.get(t).click(), sleep(3e3), storages.create("recordImageTextTime").put("recordImageTextTime", (new Date).getTime() + 1e3 * storages.create("imageTexttl").get("imageTexttl")), storages.create("jiance").get("jiance") && storages.create("taskOfImageText").put("taskOfImageText", 1), !0;
                                    e.get(t).parent().parent().child(0).clickable() ? (log("打开作品"), e.get(t).parent().parent().child(0).click()) : e.get(t).parent().parent().clickable() ? (log("打开作品"), e.get(t).parent().parent().click()) : (log("打开作品"), e.get(t).parent().parent().child(1).click()), sleep(3e3)
                                }
                                if (descContains("未点赞").findOnce(0) && descContains("未点赞").findOnce(1)) {
                                    if (storages.create("stupid").get("stupid")) {
                                        if (storages.create("stupid").get("stupid")) {
                                            if (0 == t) var r = 0;
                                            else r = 1;
                                            log("执行点赞"), descContains("未点赞").clickable(!0).findOnce(r).click(), sleep(1e3)
                                        } else 0 == random(0, 1) && (r = 0 == t ? 0 : 1, log("执行点赞"), descContains("未点赞").clickable(!0).findOnce(r).click(), sleep(1e3), 0 == random(0, 3) && exit());
                                        return storages.create("recordImageTextTime").put("recordImageTextTime", (new Date).getTime() + 1e3 * storages.create("imageTexttl").get("imageTexttl")), storages.create("jiance").get("jiance") && storages.create("taskOfImageText").put("taskOfImageText", 1), log("返回"), back(), sleep(3e3), !0
                                    }
                                    if (1 == random(1, 2)) return (storages.create("stupid").get("stupid") || 0 == random(0, 1)) && (r = 0 == t ? 0 : 1, log("执行点赞"), descContains("未点赞").clickable(!0).findOnce(r).click(), sleep(1e3)), storages.create("recordImageTextTime").put("recordImageTextTime", (new Date).getTime() + 1e3 * storages.create("imageTexttl").get("imageTexttl")), log("返回"), back(), sleep(3e3), !0
                                }
                                break
                            } return !1
                }

                /**
                 * 头像点击功能
                 * @param {string} e - 目标用户标识
                 * @returns {boolean} 头像点击是否成功
                 */
                function profilePicture(e) {
                    if (textContains(e).clickable(!0).findOnce())
                        if (1 != storages.create("headClick").get(textContains(e).clickable(!0).findOne().text()))
                            if (desc("用户头像").clickable(!0).findOnce()) {
                                if (desc("用户头像").clickable(!0).findOne().click(), sleep(3e3), text("点赞").findOnce()) return (storages.create("stupid").get("stupid") || 0 == random(0, 1)) && (click(text("点赞").findOne().bounds().centerX(), text("点赞").findOne().bounds().centerY()), sleep(1e3)), back(), sleep(1e3), textContains(e).clickable(!0).findOnce() && storages.create("headClick").put(textContains(e).clickable(!0).findOne().text(), 1), storages.create("recordProfilePictureTime").put("recordProfilePictureTime", (new Date).getTime() + 1e3 * storages.create("profilePicturetl").get("profilePicturetl")), !0
                            } else log("未识别");
                    else log("该账号头像已点赞！");
                    return !1
                }

                /**
                 * 收藏功能
                 * @returns {boolean} 收藏是否成功
                 */
                function collect() {
                    
                    if (storages.create("xihuan").get("xihuan")) {
                        var e = descContains("收藏").clickable(!0).findOnce(1);
                        return !!(e.desc().split("未选中").length > 1 && e) && ((storages.create("stupid").get("stupid") || 0 == random(0, 1)) && (log("执行收藏"), e.click(), sleep(3e3)), storages.create("recordCollectTime").put("recordCollectTime", (new Date).getTime() + 1e3 * storages.create("collecttl").get("collecttl")), !0)
                    }
                    return !(!descContains("点赞数").findOnce(0) || (descContains("点赞数").findOnce() && (descContains("点赞数").findOnce(0).parent().parent().child(0).clickable() ? (log("打开作品"), descContains("点赞数").findOnce(0).parent().parent().child(0).click()) : descContains("点赞数").findOnce(0).parent().parent().clickable() ? (log("打开作品"), descContains("点赞数").findOnce(0).parent().parent().click()) : (log("打开作品"), descContains("点赞数").findOnce(0).parent().parent().child(1).click()), sleep(3e3)), !descContains("未选中，收藏").clickable(!0).findOnce(0)) || ((storages.create("stupid").get("stupid") || 0 == random(0, 1)) && (log("执行收藏"), descContains("收藏").clickable(!0).findOne().click(), sleep(1e3)), storages.create("recordCollectTime").put("recordCollectTime", (new Date).getTime() + 1e3 * storages.create("collecttl").get("collecttl")), log("返回"), back(), sleep(3e3), 0))
                }

                /**
                 * 评论功能
                 * @param {string} idName - 应用ID名称
                 * @returns {boolean} 评论是否成功
                 */
                function comment(idName) {

                    if (storages.create("xihuan").get("xihuan")) {
                        /**
                         * 生成评论内容
                         * @returns {string} 随机生成的评论内容
                         */
                        function commentContent() {
                            var e = ["[微笑]", "[发呆]", "[酷拽]", "[捂脸]", "[害羞]", "[呲牙]", "[尬笑]", "[调皮]", "[舔屏]", "[爱心]", "[比心]", "[赞]", "[鼓掌]", "[感谢]", "[抱抱你]", "[玫瑰]", "[灵机一动]", "[耶]", "[大笑]", "[机智]", "[送心]", "[666]", "[来看我]", "[一起加油]", "[小鼓掌]", "[大金牙]", "[偷笑]", "[奸笑]", "[得意]", "[憨笑]", "[坏笑]", "[抓狂]", "[愉快]", "[互粉]", "[我想静静]", "[委屈]", "[飞吻]", "[听歌]", "[求抱抱]", "[不失礼貌的微笑]", "[干饭人]", "[吐舌]", "[摸头]", "[做鬼脸]", "[强]", "[如花]", "[惊喜]", "[奋斗]", "[吐彩虹]", "[嘿哈]", "[真的会谢]", "[好开心]", "[表面微笑]", "[表面呲牙]", "[开心兔]", "[招财兔]", "[年兽兔]", "[雪人]", "[雪花]", "[烟花]", "[福]", "[锦鲤]", "[灯笼]", "[棒棒糖]", "[巧克力]"],
                                t = ["深爱", "痴恋", "情深", "思慕", "依恋", "甜美", "蜜意", "温暖", "柔情", "心怡", "喜悦", "沉醉", "缘分", "珍惜", "缠绵", "亲密", "眷恋", "陪伴", "思念", "挚爱", "倾心", "爱慕", "钟情", "心动", "甜心", "萌爱", "热恋", "浓情", "柔蜜", "宠溺", "恋恋", "怜爱", "甜宠", "蜜恋", "蜜爱", "蜜语", "倾慕", "温存", "缱绻", "依依", "暖心", "恋歌", "情愿", "情愫", "惜爱", "心悦", "恋心", "醉心", "爱意", "依恋", "醉爱", "梦恋", "芳心", "心悦", "初恋", "真情", "柔意", "心仪", "愿意", "守候", "情思", "蜜爱", "心恋", "蜜宠", "宠爱", "情意", "心念", "恋人", "念情", "暖意", "恩爱", "爱恋", "恋慕", "思恋", "情牵", "心系", "牵挂", "心疼", "深情", "纯爱", "相恋", "萌恋", "惜缘", "情定", "怦然", "沉迷", "难舍", "依附", "陪你", "抱紧", "亲吻", "依偎", "贪恋", "无悔", "知心", "温柔", "爱心", "真爱", "情浓", "欢喜", "爱不尽", "情悠悠", "爱悠悠", "心依依", "暖洋洋", "梦幻般", "深深爱", "痴心醉", "情绵绵", "依依情", "蜜恋情", "温柔乡", "喜相逢", "爱相随", "心相印", "缘相系", "爱无悔", "恋不休", "思无尽", "念无涯", "心甘愿", "情相依", "梦相随", "暖人心", "爱一生", "愿相守", "甜如蜜", "爱常在", "情依旧", "心沉醉", "深爱你", "为你痴", "心中情", "念不忘", "梦中人", "情相牵", "倾心恋", "爱不释", "蜜意浓", "情入骨", "梦里梦", "心意浓", "真心意", "情未了", "柔似水", "爱如梦", "缘分深", "梦相思", "醉红尘", "心相依", "念如初", "一世情", "此生缘", "柔似雨", "意绵绵", "柔情深", "心相印", "恋不休", "醉倾心", "心有灵", "情未央", "心醉意", "甜如蜜", "深爱你", "梦一场", "意深深", "情似海", "恋相随", "伴余生", "梦浮生", "爱难舍", "心难移", "长相忆", "难忘怀", "爱难忘", "心相惜", "终生情", "爱之深", "情未改", "不分离", "梦一生", "终身爱", "真心恋", "心念你", "依恋你", "今生情", "爱相伴", "心相伴", "无悔爱", "伴你久", "心醉情", "恋心动", "我爱你", "爱成狂", "心心相", "情至深", "意深情", "你最爱", "无悔情", "痴情意", "心意浓", "甜蜜意", "梦随心", "深情眼", "梦相随", "心意远", "情不渝", "一世愿", "始终心", "情愫浓", "心系你", "情寄深", "初见心", "不悔爱", "长相守", "缘长久", "深爱你", "梦爱人", "情相依", "心依恋", "爱心情", "心依依", "心心爱", "情未尽", "无尽爱", "情如水", "柔情意", "最爱你", "心系你", "爱永恒", "悸动心", "不离不", "不弃你", "爱心许", "伴你行", "心一心", "为你动", "真情义", "爱相随", "拥你紧", "纯洁爱", "无悔情", "真挚情", "爱依恋", "伴你久", "此心宠", "情未了", "爱藏心", "爱如风", "心恋人", "爱不休", "只为你", "情之深", "真爱久", "情如潮", "心交心", "怀深情", "心久矣", "思你情", "恋人心", "相依行", "思念深", "浓浓爱", "暖暖心", "心难忘", "缠绵情", "万般爱", "温柔手", "情深浅", "心系你", "心为你", "一生依", "倾情似", "难舍难", "深深爱", "心里人", "唯你心", "真心意", "永久恋", "默默爱", "无尽牵", "紧握手", "深情依", "默默爱", "真心痛", "此生爱", "为你笑", "醉情深", "梦成真", "爱相随", "为你情", "爱成空", "心似水", "纯真爱", "情似火", "爱永存", "梦伴随", "情常在", "为你心", "爱未尽", "愿此生", "深情浓", "爱深深", "恋有心", "心常温", "愿共你", "永不离", "情与梦", "甜蜜心", "永相伴", "情真意", "思心随", "爱入骨", "为爱痴", "心如潮", "牵心情", "此情深", "醉在你", "真爱你", "愿为你", "情不悔", "愿相依", "甜蜜意", "爱常存", "你我心", "情难舍", "思不息", "陪伴心", "一生痴", "心永远", "相守情", "此心牵", "心为你", "心依恋", "爱如海", "情依久", "依依爱", "心如梦", "爱人心", "情意深", "思你久", "伴你行", "不离弃", "情深意", "心痴情", "为你等", "最深情", "宠爱你", "无尽深", "梦缠绵", "这个创意太绝了，完全超出我的想象！", "拍摄手法很独特，让人眼前一亮！", "色调和氛围感拿捏得恰到好处，太棒了！", "节奏把握得很好，看得特别舒服！", "构图太讲究了，每一帧都是艺术！", "光影运用得非常棒，视觉冲击力十足！", "剪辑丝滑流畅，专业级别的水准！", "音乐和画面配合得天衣无缝，太绝了！", "内容超级有趣，笑到停不下来！", "创意和执行力都拉满了，佩服！", "这色调爱了，电影感十足！", "拍摄手法高级感满满，超赞！", "真的太会拍了，每个角度都很讲究！", "画面质感超棒，堪比大片！", "剪辑节奏刚刚好，带感又不拖沓！", "视觉冲击力满分，真的震撼到我了！", "太有想法了，真的佩服你的脑洞！", "镜头切换自然流畅，完全沉浸其中！", "真的把氛围感拉满了，艺术感爆棚！", "运镜好丝滑，看得太过瘾了！", "太专业了，根本不像素人作品！", "有种大片的既视感，拍摄太牛了！", "每一帧都像壁纸，画面质感炸裂！", "色彩搭配超棒，视觉享受拉满！", "音乐节奏和画面配合太完美了！", "真的把情绪渲染得淋漓尽致！", "太抓人了，看完还想再看一遍！", "每个镜头都很有讲究，厉害！", "有电影级的质感，完全被吸引住了！", "不愧是大神，每次作品都很惊艳！", "全程无尿点，一秒都不舍得快进！", "故事性超强，代入感很强！", "每次都能带来惊喜，太有才了！", "这画面和配乐搭配得太舒服了！", "拍摄思路太清晰了，厉害！", "光影效果太棒了，视觉冲击力满满！", "整体氛围感塑造得太好了！", "你的作品每次都很有深度！", "创意和技术完美结合，绝了！", "不愧是抖音高手，太强了！", "这细节把控得太好了，佩服！", "镜头感超强，专业级别！", "拍摄的感觉太治愈了，爱了！", "超有高级感，质感拉满！", "这氛围感真的太绝了！", "每一帧都像艺术品，厉害！", "剪辑功底太扎实了，佩服！", "拍出了大片的感觉，好牛！", "你的作品总是充满惊喜！", "真的太有才华了，羡慕！", "画面超有故事感，太棒了！", "配色太高级了，好喜欢！", "节奏把握得刚刚好，看得好舒服！", "这音乐和画面结合得完美无缺！", "氛围感和故事感都拉满了！", "看得太爽了，完美！", "滤镜和色调搭配太有感觉了！", "你的作品就是视觉享受！", "剧情、拍摄、剪辑都太棒了！", "有专业导演的水准了！", "这个视角选得太好了！", "完美诠释了什么叫艺术感！", "真的太会拍了，欣赏不来都难！", "运镜太流畅了，丝滑得不行！", "太炸裂了，根本停不下来！", "太有想象力了，脑洞清奇！", "完美拿捏了观众的审美！", "整个作品都非常高级！", "每个细节都做得无可挑剔！", "这配色和光影太舒服了！", "整个氛围感塑造得太完美了！", "你的视频总是能带给人惊喜！", "完美呈现了视觉美学！", "每个镜头都充满张力，太牛了！", "这剪辑手法真的太棒了！", "太懂怎么调动观众情绪了！", "光影运用和色彩搭配都太棒了！", "真的拍出了电影感，超棒！", "你的作品质量太高了，佩服！", "天生就适合拍短视频，绝了！", "构图太考究了，太专业了！", "这拍摄手法真的绝了！", "镜头切换流畅得像流水一样！", "光线运用得太棒了，太好看了！", "剧情流畅，拍摄手法满分！", "你的作品每次都能给人惊喜！", "真的有电影级别的质感！", "拍摄手法和创意真的满分！", "绝对的短视频高手，佩服！", "不愧是抖音大神，实至名归！", "这节奏感和氛围感绝了！", "你的视频总是那么有感染力！", "整体画面感很强，特别舒服！", "内容有趣，画面精致，绝了！", "太抓人眼球了，一秒都不想错过！", "艺术感和视觉冲击力都很强！", "真的太喜欢你的作品了，每次都很惊艳！", "你怎么这么可爱，简直像童话里走出来的小精灵！", "你的笑容比太阳还暖，看到就开心！", "每次看到你，都觉得世界变得更美好了！", "你就像是一颗糖，甜到我心里了！", "怎么会有这么可爱的人，完全被你融化了！", "你的可爱指数已经突破天际了！", "你就像小奶猫一样，超级软萌！", "你就是行走的治愈神器，看到你心情都变好了！", "可可爱爱，谁看了不心动？", "你的可爱已经超越了次元壁！", "你是怎么做到连眨眼都这么萌的？", "你的每一个表情都超级Q弹可爱！", "软萌的样子，让人想抱抱！", "你是上天派来的小可爱吧？", "你的可爱就像春天的风一样，轻轻拂过心田！", "你一定是被糖果泡大的，不然怎么这么甜？", "世界上怎么会有这么可爱的小仙女/小王子！", "你是可爱本人认证的代表！", "谁允许你这么可爱了？我要举报你犯规！", "可爱炸了，感觉自己的心都要被萌化了！", "光是看着你，就觉得生活变得好甜！", "天哪，你的可爱程度已经超标了！", "遇见你之后，其他的可爱都变成了陪衬！", "你的眼睛会说话吧？里面满满的可爱因子！", "你就是个小天使吧，太萌了！", "谁给你的勇气这么可爱！", "你是小熊软糖变的吗？怎么看都软乎乎的！", "你就是可爱本尊吧，实名羡慕！", "你的笑容甜得像蜂蜜一样，让人沉醉！", "感觉你浑身都散发着可爱的气息！", "你的可爱像泡泡一样，轻轻一戳就心动了！", "你笑起来的样子，像夏天的冰淇淋，甜到心里！", "可爱+元气=你，完美组合！", "你一定是吃可爱长大的，不然怎么这么迷人！", "你就是现实版的卡通人物吧，太萌了！", "太犯规了，谁让你这么可爱！", "你的可爱让我沦陷了！", "你是甜甜的小星球，看到你整个人都亮了！", "这世间的可爱，八成都被你承包了吧！", "看你一眼，心都化成棉花糖了！", "你的存在就是对可爱二字最好的诠释！", "可爱这种东西，不是应该按克卖吗？怎么你满满一大桶！", "太萌了，简直想把你装进口袋随身携带！", "你笑起来的样子，像极了春天的第一缕阳光！", "你的可爱是天生的，根本藏不住！", "你是软萌天使吧，连说话都自带甜味！", "你的可爱让我毫无招架之力！", "你的可爱指数已经远超极限值！", "你一定是用糖果做的吧，不然怎么这么甜！", "小可爱，请停止散发你的魅力，我快受不了了！", "你的存在就是一种可爱的象征！", "看到你，连烦恼都会自动消失！", "不行了，你的可爱已经把我包围了！", "天啊，你怎么可以这么萌！", "你的微笑让整个世界都亮了！", "你就是传说中的可爱制造机吧？", "快看看你的身份证，确认你不是可爱本人？", "光是看着你，就忍不住嘴角上扬！", "你走路的样子都带着可爱的小旋风！", "简直就是童话故事里的小公主/小王子！", "你是不是偷偷吃了可爱果？", "你的可爱能让整个银河都发光！", "拜托，可爱是会传染的吗？因为看了你之后我也变可爱了！", "被你的可爱360度无死角狙击了！", "你是小天使吧？自带萌萌的光环！", "你的可爱度已经满格了！", "全世界的糖果加起来，都没有你甜！", "你的可爱让我原地融化了！", "看你一眼，心情都会变好！", "你是个行走的开心果，看到你就忍不住笑！", "谁允许你这么可爱？快给我发个可爱许可证！", "你的可爱是最有感染力的魔法！", "全世界的可爱都集中在你身上了吧？", "你简直是人间小甜饼，超想咬一口！", "你是冬天里的一杯热可可，暖心又可爱！", "你的可爱让人想抱抱个不停！", "别再可爱了，我的心已经融化了！", "这么可爱的你，必须要被世界收藏起来！", "你的一举一动都自带萌点！", "这世界上怎么会有你这么Q弹可爱的人！", "你的可爱简直是上天的恩赐！", "你就像春天的小鹿，一蹦一跳的样子太可爱了！", "你是不是用童话故事里的魔法变出来的？", "全世界的宠爱都该属于你这么可爱的人！", "你笑起来的样子，像天上的小星星，闪闪发光！", "你的可爱真的会让人心动不已！", "你真的是活力满满的小太阳，照亮了我的世界！", "你的存在，就是一种让人无法抗拒的可爱魔力！", "从今天起，你的名字就叫可爱本可！", "你走到哪，哪里就充满了温暖和快乐！", "你的可爱程度远远超过了所有人的想象！", "世界上最甜的东西都比不上你的可爱！", "你的每一个表情都像小奶猫一样萌！", "谁都无法拒绝你的可爱，真的太喜欢你了！", "你的可爱，已经刻进我的DNA里了！", "如果可爱是种罪，那你已经无期徒刑了！", "你的可爱，是宇宙级别的！", "你的帅气已经突破了天际！", "帅得让人窒息，完全移不开眼！", "颜值爆表，帅得像从漫画里走出来的！", "你的帅气自带光环，让人无法忽视！", "简直是天生的男主脸，太完美了！", "每个角度都帅得让人心动！", "你这张脸，简直是神仙精雕细琢出来的！", "不愧是天选之子，这气质无敌了！", "这世上怎么会有你这么帅的人，犯规了吧！", "帅得让人沦陷，无法自拔！", "就算站在人群里，你也是最耀眼的那一个！", "你的帅气是标准模版，所有人的理想型！", "长得帅就算了，气质还这么绝！", "不仅帅，还有一种让人移不开眼的魅力！", "你随便一站，就是一道风景！", "天啊，你这张脸真的太逆天了！", "帅得人神共愤，简直不给别人留活路！", "你的颜值，已经超越了地球人的标准！", "你走路带风的样子，帅气值直接拉满！", "这是什么神仙五官搭配，简直完美！", "你的帅气是天生的，根本藏不住！", "五官精致，轮廓分明，太帅了吧！", "光是站在那里，就帅得让人心跳加速！", "你的帅气让我想写一首诗来赞美！", "根本就是现实版的王子！", "这颜值，这气质，简直是偶像剧男主本主！", "随手一拍都是时尚大片，太绝了！", "世界欠我一个像你这样的帅哥！", "你就是行走的衣架子，穿什么都帅！", "难道你是天使下凡？长得这么帅合理吗！", "你的颜值就是360度无死角的那种！", "根本不需要滤镜，你的帅气已经自带效果！", "简直像从画里走出来的帅哥！", "你的五官组合在一起，完美得不科学！", "不管是什么风格，你都能帅出新高度！", "你连不经意的一个回眸都帅到让人心动！", "上天真的很偏心，把所有的帅气都给了你！", "帅气的脸庞加上迷人的气质，太吸引人了！", "如果帅是一种罪，你早就罪无可赦了！", "为什么你随便一个动作都这么有魅力！", "你的帅气不仅仅是外表，更是一种气场！", "你的侧颜杀，简直能让人心跳漏半拍！", "这颜值，走到哪里都能成为焦点！", "你的帅气是一种让人上瘾的毒药！", "你真的是别人家的男朋友标准！", "帅到让我词穷，只能不断重复“太帅了”！", "颜值天花板，没错就是你！", "如果帅可以兑换财富，你早就是世界首富了！", "你帅得如此低调，却依旧掩盖不住光芒！", "你的笑容太杀人了，简直是心脏狙击机！", "你的颜值和气质，简直是绝配！", "穿衣有品，气质满分，帅得毫不费力！", "长得帅也就算了，连气场都这么迷人！", "这是什么神仙颜值，根本不敢直视！", "你的帅不是张扬的那种，而是让人越看越上头！", "你帅得不止有颜值，还有让人臣服的气质！", "你是行走的荷尔蒙，每个动作都那么迷人！", "你的眼神深邃得像夜空，帅得让我迷失了！", "你不仅帅，还自带优雅气质，太完美了！", "你的帅气不是普通的帅，而是精致到极致的帅！", "你一笑，整个世界都变得温暖起来了！", "你的帅气太有层次感了，越看越耐看！", "你是传说中的帅气代名词吧！", "你是行走的撩人机器，光是看着就心动！", "你帅得像一道闪电，一出现就照亮整个世界！", "你的颜值足以让任何人一见倾心！", "这五官，这身材，这气质，简直完美无缺！", "长得帅也就算了，笑起来还那么有杀伤力！", "你随便一站，就像是电影里的主角！", "你的帅，是与生俱来的贵族气质！", "每次看到你，都觉得世界变得更美好了！", "就算你什么都不做，光是站在那里就已经很帅了！", "你自带贵族气息，帅得让人敬仰！", "你的帅不仅仅是外表，更是由内而外散发出的魅力！", "世界上怎么会有你这么帅的人？不科学！", "你的侧脸简直可以用来当艺术雕塑的模版！", "你一出现，所有的风景都失去了光彩！", "你的帅气让我眼花缭乱，根本看不够！", "你的颜值已经不属于凡人了，根本是天神级别的！", "如果帅是一种超能力，那你肯定是最强的那个！", "你就是行走的画报，每一帧都像大片！", "你的五官太精致了，每一处都完美无瑕！", "光是听你的声音，都觉得超级有魅力！", "你的帅，不是普通的帅，是独一无二的帅！", "无论是阳光型还是冷酷型，你都能完美驾驭！", "如果帅气有排行榜，你绝对是第一名！", "你自带王者气质，帅得让人忍不住臣服！", "你的存在就是帅气的象征！", "你是那种让人看一眼就沦陷的帅！", "你是天生的焦点，无论走到哪里都备受瞩目！", "你的帅气不只是外表，而是一种无法抗拒的魅力！", "看了你一眼，才发现什么叫真正的帅哥！", "你一笑，我的心跳就加速了！", "如果帅能成为一种职业，你绝对是行业巅峰！", "你的才华令人惊叹！", "你的智慧简直无人能及！", "你总是能想出绝妙的解决方案！", "你的创新能力让我佩服不已！", "你的领导能力无人能敌！", "你的逻辑思维太强了！", "你的代码简洁又高效，真是高手！", "你的学习能力令人敬佩！", "你的眼光总是那么独到！", "你真是团队的灵魂人物！", "你的执行力超强！", "你总能迅速抓住问题的关键！", "你的专业知识太丰富了！", "你的判断力非常精准！", "你的耐心和毅力让人敬佩！", "你的分析能力超群！", "你的演讲能力让人折服！", "你的沟通能力超级强！", "你的表达能力太厉害了！", "你的工作效率高得惊人！", "你的思维方式非常独特！", "你的责任心让人放心！", "你的领导风格令人钦佩！", "你的技术水平一流！", "你的创造力源源不断！", "你的解决问题能力太强了！", "你的想法总是那么新颖！", "你的适应能力超强！", "你的写作能力令人赞叹！", "你的设计感太棒了！", "你的团队协作能力很优秀！", "你的冷静应对能力让人佩服！", "你的观察力太敏锐了！", "你的幽默感让人放松！", "你的学习速度快得惊人！", "你的自律能力让人羡慕！", "你的判断力和远见太棒了！", "你的心理素质真强！", "你的决策能力令人信服！", "你的自信非常有感染力！", "你的经验非常丰富！", "你的拼搏精神值得学习！", "你的领导魅力无人能及！", "你的执行力简直满分！", "你的事业心让人佩服！", "你的分析能力非常专业！", "你的创新思维无可挑剔！", "你的直觉总是那么准确！", "你的专注力令人惊叹！", "你的勇气让人敬佩！", "你的毅力真的无人能比！", "你的反应能力超级快！", "你的创造力无穷无尽！", "你的表达能力让人惊艳！", "你的耐心让人动容！", "你的效率实在太高了！", "你的工作态度非常敬业！", "你的专业能力值得信赖！", "你的逻辑性极强！", "你的团队精神令人敬佩！", "你的品味非常高级！", "你的风格独具魅力！", "你的思维方式特别棒！", "你的适应能力超级强！", "你的奋斗精神令人动容！", "你的管理能力太厉害了！", "你的敬业精神让人佩服！", "你的表达方式特别有感染力！", "你的学习精神让我敬佩！", "你的语言组织能力非常强！", "你的幽默让人忍俊不禁！", "你的直觉真是神准！", "你的洞察力无人能及！", "你的执行力非常强！", "你的时间管理能力超级棒！", "你的规划能力令人惊叹！", "你的专注力太强了！", "你的积极态度让人欣赏！", "你的决策能力太棒了！", "你的成长速度惊人！", "你的自信特别有感染力！", "你的技术能力非常精湛！", "你的社交能力无与伦比！", "你的演讲才能令人折服！", "你的协作能力令人钦佩！", "你的气场太强大了！", "你的谈判技巧超级厉害！", "你的写作水平极高！", "你的策划能力无人能及！", "你的统筹能力非常棒！", "你的动手能力太强了！", "你的专业水准令人惊叹！", "你的心理素质极其稳定！", "你的想象力特别丰富！", "你的变通能力超强！", "你的情商真的太高了！", "你的分析判断能力无可挑剔！", "你的思维逻辑超级缜密！", "你的业务能力让人佩服！", "你的谈吐特别有魅力！", "你的表达方式很有说服力！", "你的组织能力真的超强！"],
                                r = [1, 2, 3],
                                a = "",
                                s = random(0, r.length - 1);
                            if (1 == r[s])
                                for (var n = random(1, 3), i = 0; i < n; i++) "" == a ? a = e[random(0, e.length - 1)] : a += e[random(0, e.length - 1)];
                            else if (2 == r[s])
                                for (n = random(1, 6), i = 0; i < n; i++) a = "" == a ? t[random(0, t.length - 1)] : a + " " + t[random(0, t.length - 1)];
                            else if (3 == r[s])
                                for (n = random(1, 6), i = 0; i < n; i++) a = "" == a ? t[random(0, t.length - 1)] : a + e[random(0, e.length - 1)] + t[random(0, t.length - 1)];
                            return a
                        }
                        var biaoqing = desc("表情").clickable(!0).findOnce();
                        if (!biaoqing) {
                            // 向服务器请求验证是否允许进入粉丝列表（基于新搜索面状态）
                            //修改8 是否允许进入粉丝界面 1 0
                            let result = storages.create("newSearchFace").get("newSearchFace"); //eval(serverRequest("enterConventionDateToEnterFanList_1", storages.create("newSearchFace").get("newSearchFace")));
                            if ("com.ss.android.yumme.video" == idName || result) {
                                var pinglun = descContains("评论").clickable(!0).findOnce(1);
                                pinglun && (pinglun.click(), sleep(3e3))
                            }
                        }
                        if (biaoqing) {
                            log("点击表情"), biaoqing.click(), sleep(1e3), log("录入文案"), setText(commentContent()), sleep(1e3);
                            var fasong = text("发送").clickable(!0).findOne(5e3);
                            if (fasong) return (storages.create("stupid").get("stupid") || random(0, !1)) && (log("发送"), fasong.click(), sleep(3e3)), storages.create("recordCommentTime").put("recordCommentTime", (new Date).getTime() + 1e3 * storages.create("commenttl").get("commenttl")), !0
                        }
                        return !1
                    } {
                        function commentContent() {
                            var e = ["[微笑]", "[发呆]", "[酷拽]", "[捂脸]", "[害羞]", "[呲牙]", "[尬笑]", "[调皮]", "[舔屏]", "[爱心]", "[比心]", "[赞]", "[鼓掌]", "[感谢]", "[抱抱你]", "[玫瑰]", "[灵机一动]", "[耶]", "[大笑]", "[机智]", "[送心]", "[666]", "[来看我]", "[一起加油]", "[小鼓掌]", "[大金牙]", "[偷笑]", "[奸笑]", "[得意]", "[憨笑]", "[坏笑]", "[抓狂]", "[愉快]", "[互粉]", "[我想静静]", "[委屈]", "[飞吻]", "[听歌]", "[求抱抱]", "[不失礼貌的微笑]", "[干饭人]", "[吐舌]", "[摸头]", "[做鬼脸]", "[强]", "[如花]", "[惊喜]", "[奋斗]", "[吐彩虹]", "[嘿哈]", "[真的会谢]", "[好开心]", "[表面微笑]", "[表面呲牙]", "[开心兔]", "[招财兔]", "[年兽兔]", "[雪人]", "[雪花]", "[烟花]", "[福]", "[锦鲤]", "[灯笼]", "[棒棒糖]", "[巧克力]"],
                                t = ["深爱", "痴恋", "情深", "思慕", "依恋", "甜美", "蜜意", "温暖", "柔情", "心怡", "喜悦", "沉醉", "缘分", "珍惜", "缠绵", "亲密", "眷恋", "陪伴", "思念", "挚爱", "倾心", "爱慕", "钟情", "心动", "甜心", "萌爱", "热恋", "浓情", "柔蜜", "宠溺", "恋恋", "怜爱", "甜宠", "蜜恋", "蜜爱", "蜜语", "倾慕", "温存", "缱绻", "依依", "暖心", "恋歌", "情愿", "情愫", "惜爱", "心悦", "恋心", "醉心", "爱意", "依恋", "醉爱", "梦恋", "芳心", "心悦", "初恋", "真情", "柔意", "心仪", "愿意", "守候", "情思", "蜜爱", "心恋", "蜜宠", "宠爱", "情意", "心念", "恋人", "念情", "暖意", "恩爱", "爱恋", "恋慕", "思恋", "情牵", "心系", "牵挂", "心疼", "深情", "纯爱", "相恋", "萌恋", "惜缘", "情定", "怦然", "沉迷", "难舍", "依附", "陪你", "抱紧", "亲吻", "依偎", "贪恋", "无悔", "知心", "温柔", "爱心", "真爱", "情浓", "欢喜", "爱不尽", "情悠悠", "爱悠悠", "心依依", "暖洋洋", "梦幻般", "深深爱", "痴心醉", "情绵绵", "依依情", "蜜恋情", "温柔乡", "喜相逢", "爱相随", "心相印", "缘相系", "爱无悔", "恋不休", "思无尽", "念无涯", "心甘愿", "情相依", "梦相随", "暖人心", "爱一生", "愿相守", "甜如蜜", "爱常在", "情依旧", "心沉醉", "深爱你", "为你痴", "心中情", "念不忘", "梦中人", "情相牵", "倾心恋", "爱不释", "蜜意浓", "情入骨", "梦里梦", "心意浓", "真心意", "情未了", "柔似水", "爱如梦", "缘分深", "梦相思", "醉红尘", "心相依", "念如初", "一世情", "此生缘", "柔似雨", "意绵绵", "柔情深", "心相印", "恋不休", "醉倾心", "心有灵", "情未央", "心醉意", "甜如蜜", "深爱你", "梦一场", "意深深", "情似海", "恋相随", "伴余生", "梦浮生", "爱难舍", "心难移", "长相忆", "难忘怀", "爱难忘", "心相惜", "终生情", "爱之深", "情未改", "不分离", "梦一生", "终身爱", "真心恋", "心念你", "依恋你", "今生情", "爱相伴", "心相伴", "无悔爱", "伴你久", "心醉情", "恋心动", "我爱你", "爱成狂", "心心相", "情至深", "意深情", "你最爱", "无悔情", "痴情意", "心意浓", "甜蜜意", "梦随心", "深情眼", "梦相随", "心意远", "情不渝", "一世愿", "始终心", "情愫浓", "心系你", "情寄深", "初见心", "不悔爱", "长相守", "缘长久", "深爱你", "梦爱人", "情相依", "心依恋", "爱心情", "心依依", "心心爱", "情未尽", "无尽爱", "情如水", "柔情意", "最爱你", "心系你", "爱永恒", "悸动心", "不离不", "不弃你", "爱心许", "伴你行", "心一心", "为你动", "真情义", "爱相随", "拥你紧", "纯洁爱", "无悔情", "真挚情", "爱依恋", "伴你久", "此心宠", "情未了", "爱藏心", "爱如风", "心恋人", "爱不休", "只为你", "情之深", "真爱久", "情如潮", "心交心", "怀深情", "心久矣", "思你情", "恋人心", "相依行", "思念深", "浓浓爱", "暖暖心", "心难忘", "缠绵情", "万般爱", "温柔手", "情深浅", "心系你", "心为你", "一生依", "倾情似", "难舍难", "深深爱", "心里人", "唯你心", "真心意", "永久恋", "默默爱", "无尽牵", "紧握手", "深情依", "默默爱", "真心痛", "此生爱", "为你笑", "醉情深", "梦成真", "爱相随", "为你情", "爱成空", "心似水", "纯真爱", "情似火", "爱永存", "梦伴随", "情常在", "为你心", "爱未尽", "愿此生", "深情浓", "爱深深", "恋有心", "心常温", "愿共你", "永不离", "情与梦", "甜蜜心", "永相伴", "情真意", "思心随", "爱入骨", "为爱痴", "心如潮", "牵心情", "此情深", "醉在你", "真爱你", "愿为你", "情不悔", "愿相依", "甜蜜意", "爱常存", "你我心", "情难舍", "思不息", "陪伴心", "一生痴", "心永远", "相守情", "此心牵", "心为你", "心依恋", "爱如海", "情依久", "依依爱", "心如梦", "爱人心", "情意深", "思你久", "伴你行", "不离弃", "情深意", "心痴情", "为你等", "最深情", "宠爱你", "无尽深", "梦缠绵", "这个创意太绝了，完全超出我的想象！", "拍摄手法很独特，让人眼前一亮！", "色调和氛围感拿捏得恰到好处，太棒了！", "节奏把握得很好，看得特别舒服！", "构图太讲究了，每一帧都是艺术！", "光影运用得非常棒，视觉冲击力十足！", "剪辑丝滑流畅，专业级别的水准！", "音乐和画面配合得天衣无缝，太绝了！", "内容超级有趣，笑到停不下来！", "创意和执行力都拉满了，佩服！", "这色调爱了，电影感十足！", "拍摄手法高级感满满，超赞！", "真的太会拍了，每个角度都很讲究！", "画面质感超棒，堪比大片！", "剪辑节奏刚刚好，带感又不拖沓！", "视觉冲击力满分，真的震撼到我了！", "太有想法了，真的佩服你的脑洞！", "镜头切换自然流畅，完全沉浸其中！", "真的把氛围感拉满了，艺术感爆棚！", "运镜好丝滑，看得太过瘾了！", "太专业了，根本不像素人作品！", "有种大片的既视感，拍摄太牛了！", "每一帧都像壁纸，画面质感炸裂！", "色彩搭配超棒，视觉享受拉满！", "音乐节奏和画面配合太完美了！", "真的把情绪渲染得淋漓尽致！", "太抓人了，看完还想再看一遍！", "每个镜头都很有讲究，厉害！", "有电影级的质感，完全被吸引住了！", "不愧是大神，每次作品都很惊艳！", "全程无尿点，一秒都不舍得快进！", "故事性超强，代入感很强！", "每次都能带来惊喜，太有才了！", "这画面和配乐搭配得太舒服了！", "拍摄思路太清晰了，厉害！", "光影效果太棒了，视觉冲击力满满！", "整体氛围感塑造得太好了！", "你的作品每次都很有深度！", "创意和技术完美结合，绝了！", "不愧是抖音高手，太强了！", "这细节把控得太好了，佩服！", "镜头感超强，专业级别！", "拍摄的感觉太治愈了，爱了！", "超有高级感，质感拉满！", "这氛围感真的太绝了！", "每一帧都像艺术品，厉害！", "剪辑功底太扎实了，佩服！", "拍出了大片的感觉，好牛！", "你的作品总是充满惊喜！", "真的太有才华了，羡慕！", "画面超有故事感，太棒了！", "配色太高级了，好喜欢！", "节奏把握得刚刚好，看得好舒服！", "这音乐和画面结合得完美无缺！", "氛围感和故事感都拉满了！", "看得太爽了，完美！", "滤镜和色调搭配太有感觉了！", "你的作品就是视觉享受！", "剧情、拍摄、剪辑都太棒了！", "有专业导演的水准了！", "这个视角选得太好了！", "完美诠释了什么叫艺术感！", "真的太会拍了，欣赏不来都难！", "运镜太流畅了，丝滑得不行！", "太炸裂了，根本停不下来！", "太有想象力了，脑洞清奇！", "完美拿捏了观众的审美！", "整个作品都非常高级！", "每个细节都做得无可挑剔！", "这配色和光影太舒服了！", "整个氛围感塑造得太完美了！", "你的视频总是能带给人惊喜！", "完美呈现了视觉美学！", "每个镜头都充满张力，太牛了！", "这剪辑手法真的太棒了！", "太懂怎么调动观众情绪了！", "光影运用和色彩搭配都太棒了！", "真的拍出了电影感，超棒！", "你的作品质量太高了，佩服！", "天生就适合拍短视频，绝了！", "构图太考究了，太专业了！", "这拍摄手法真的绝了！", "镜头切换流畅得像流水一样！", "光线运用得太棒了，太好看了！", "剧情流畅，拍摄手法满分！", "你的作品每次都能给人惊喜！", "真的有电影级别的质感！", "拍摄手法和创意真的满分！", "绝对的短视频高手，佩服！", "不愧是抖音大神，实至名归！", "这节奏感和氛围感绝了！", "你的视频总是那么有感染力！", "整体画面感很强，特别舒服！", "内容有趣，画面精致，绝了！", "太抓人眼球了，一秒都不想错过！", "艺术感和视觉冲击力都很强！", "真的太喜欢你的作品了，每次都很惊艳！", "你怎么这么可爱，简直像童话里走出来的小精灵！", "你的笑容比太阳还暖，看到就开心！", "每次看到你，都觉得世界变得更美好了！", "你就像是一颗糖，甜到我心里了！", "怎么会有这么可爱的人，完全被你融化了！", "你的可爱指数已经突破天际了！", "你就像小奶猫一样，超级软萌！", "你就是行走的治愈神器，看到你心情都变好了！", "可可爱爱，谁看了不心动？", "你的可爱已经超越了次元壁！", "你是怎么做到连眨眼都这么萌的？", "你的每一个表情都超级Q弹可爱！", "软萌的样子，让人想抱抱！", "你是上天派来的小可爱吧？", "你的可爱就像春天的风一样，轻轻拂过心田！", "你一定是被糖果泡大的，不然怎么这么甜？", "世界上怎么会有这么可爱的小仙女/小王子！", "你是可爱本人认证的代表！", "谁允许你这么可爱了？我要举报你犯规！", "可爱炸了，感觉自己的心都要被萌化了！", "光是看着你，就觉得生活变得好甜！", "天哪，你的可爱程度已经超标了！", "遇见你之后，其他的可爱都变成了陪衬！", "你的眼睛会说话吧？里面满满的可爱因子！", "你就是个小天使吧，太萌了！", "谁给你的勇气这么可爱！", "你是小熊软糖变的吗？怎么看都软乎乎的！", "你就是可爱本尊吧，实名羡慕！", "你的笑容甜得像蜂蜜一样，让人沉醉！", "感觉你浑身都散发着可爱的气息！", "你的可爱像泡泡一样，轻轻一戳就心动了！", "你笑起来的样子，像夏天的冰淇淋，甜到心里！", "可爱+元气=你，完美组合！", "你一定是吃可爱长大的，不然怎么这么迷人！", "你就是现实版的卡通人物吧，太萌了！", "太犯规了，谁让你这么可爱！", "你的可爱让我沦陷了！", "你是甜甜的小星球，看到你整个人都亮了！", "这世间的可爱，八成都被你承包了吧！", "看你一眼，心都化成棉花糖了！", "你的存在就是对可爱二字最好的诠释！", "可爱这种东西，不是应该按克卖吗？怎么你满满一大桶！", "太萌了，简直想把你装进口袋随身携带！", "你笑起来的样子，像极了春天的第一缕阳光！", "你的可爱是天生的，根本藏不住！", "你是软萌天使吧，连说话都自带甜味！", "你的可爱让我毫无招架之力！", "你的可爱指数已经远超极限值！", "你一定是用糖果做的吧，不然怎么这么甜！", "小可爱，请停止散发你的魅力，我快受不了了！", "你的存在就是一种可爱的象征！", "看到你，连烦恼都会自动消失！", "不行了，你的可爱已经把我包围了！", "天啊，你怎么可以这么萌！", "你的微笑让整个世界都亮了！", "你就是传说中的可爱制造机吧？", "快看看你的身份证，确认你不是可爱本人？", "光是看着你，就忍不住嘴角上扬！", "你走路的样子都带着可爱的小旋风！", "简直就是童话故事里的小公主/小王子！", "你是不是偷偷吃了可爱果？", "你的可爱能让整个银河都发光！", "拜托，可爱是会传染的吗？因为看了你之后我也变可爱了！", "被你的可爱360度无死角狙击了！", "你是小天使吧？自带萌萌的光环！", "你的可爱度已经满格了！", "全世界的糖果加起来，都没有你甜！", "你的可爱让我原地融化了！", "看你一眼，心情都会变好！", "你是个行走的开心果，看到你就忍不住笑！", "谁允许你这么可爱？快给我发个可爱许可证！", "你的可爱是最有感染力的魔法！", "全世界的可爱都集中在你身上了吧？", "你简直是人间小甜饼，超想咬一口！", "你是冬天里的一杯热可可，暖心又可爱！", "你的可爱让人想抱抱个不停！", "别再可爱了，我的心已经融化了！", "这么可爱的你，必须要被世界收藏起来！", "你的一举一动都自带萌点！", "这世界上怎么会有你这么Q弹可爱的人！", "你的可爱简直是上天的恩赐！", "你就像春天的小鹿，一蹦一跳的样子太可爱了！", "你是不是用童话故事里的魔法变出来的？", "全世界的宠爱都该属于你这么可爱的人！", "你笑起来的样子，像天上的小星星，闪闪发光！", "你的可爱真的会让人心动不已！", "你真的是活力满满的小太阳，照亮了我的世界！", "你的存在，就是一种让人无法抗拒的可爱魔力！", "从今天起，你的名字就叫可爱本可！", "你走到哪，哪里就充满了温暖和快乐！", "你的可爱程度远远超过了所有人的想象！", "世界上最甜的东西都比不上你的可爱！", "你的每一个表情都像小奶猫一样萌！", "谁都无法拒绝你的可爱，真的太喜欢你了！", "你的可爱，已经刻进我的DNA里了！", "如果可爱是种罪，那你已经无期徒刑了！", "你的可爱，是宇宙级别的！", "你的帅气已经突破了天际！", "帅得让人窒息，完全移不开眼！", "颜值爆表，帅得像从漫画里走出来的！", "你的帅气自带光环，让人无法忽视！", "简直是天生的男主脸，太完美了！", "每个角度都帅得让人心动！", "你这张脸，简直是神仙精雕细琢出来的！", "不愧是天选之子，这气质无敌了！", "这世上怎么会有你这么帅的人，犯规了吧！", "帅得让人沦陷，无法自拔！", "就算站在人群里，你也是最耀眼的那一个！", "你的帅气是标准模版，所有人的理想型！", "长得帅就算了，气质还这么绝！", "不仅帅，还有一种让人移不开眼的魅力！", "你随便一站，就是一道风景！", "天啊，你这张脸真的太逆天了！", "帅得人神共愤，简直不给别人留活路！", "你的颜值，已经超越了地球人的标准！", "你走路带风的样子，帅气值直接拉满！", "这是什么神仙五官搭配，简直完美！", "你的帅气是天生的，根本藏不住！", "五官精致，轮廓分明，太帅了吧！", "光是站在那里，就帅得让人心跳加速！", "你的帅气让我想写一首诗来赞美！", "根本就是现实版的王子！", "这颜值，这气质，简直是偶像剧男主本主！", "随手一拍都是时尚大片，太绝了！", "世界欠我一个像你这样的帅哥！", "你就是行走的衣架子，穿什么都帅！", "难道你是天使下凡？长得这么帅合理吗！", "你的颜值就是360度无死角的那种！", "根本不需要滤镜，你的帅气已经自带效果！", "简直像从画里走出来的帅哥！", "你的五官组合在一起，完美得不科学！", "不管是什么风格，你都能帅出新高度！", "你连不经意的一个回眸都帅到让人心动！", "上天真的很偏心，把所有的帅气都给了你！", "帅气的脸庞加上迷人的气质，太吸引人了！", "如果帅是一种罪，你早就罪无可赦了！", "为什么你随便一个动作都这么有魅力！", "你的帅气不仅仅是外表，更是一种气场！", "你的侧颜杀，简直能让人心跳漏半拍！", "这颜值，走到哪里都能成为焦点！", "你的帅气是一种让人上瘾的毒药！", "你真的是别人家的男朋友标准！", "帅到让我词穷，只能不断重复“太帅了”！", "颜值天花板，没错就是你！", "如果帅可以兑换财富，你早就是世界首富了！", "你帅得如此低调，却依旧掩盖不住光芒！", "你的笑容太杀人了，简直是心脏狙击机！", "你的颜值和气质，简直是绝配！", "穿衣有品，气质满分，帅得毫不费力！", "长得帅也就算了，连气场都这么迷人！", "这是什么神仙颜值，根本不敢直视！", "你的帅不是张扬的那种，而是让人越看越上头！", "你帅得不止有颜值，还有让人臣服的气质！", "你是行走的荷尔蒙，每个动作都那么迷人！", "你的眼神深邃得像夜空，帅得让我迷失了！", "你不仅帅，还自带优雅气质，太完美了！", "你的帅气不是普通的帅，而是精致到极致的帅！", "你一笑，整个世界都变得温暖起来了！", "你的帅气太有层次感了，越看越耐看！", "你是传说中的帅气代名词吧！", "你是行走的撩人机器，光是看着就心动！", "你帅得像一道闪电，一出现就照亮整个世界！", "你的颜值足以让任何人一见倾心！", "这五官，这身材，这气质，简直完美无缺！", "长得帅也就算了，笑起来还那么有杀伤力！", "你随便一站，就像是电影里的主角！", "你的帅，是与生俱来的贵族气质！", "每次看到你，都觉得世界变得更美好了！", "就算你什么都不做，光是站在那里就已经很帅了！", "你自带贵族气息，帅得让人敬仰！", "你的帅不仅仅是外表，更是由内而外散发出的魅力！", "世界上怎么会有你这么帅的人？不科学！", "你的侧脸简直可以用来当艺术雕塑的模版！", "你一出现，所有的风景都失去了光彩！", "你的帅气让我眼花缭乱，根本看不够！", "你的颜值已经不属于凡人了，根本是天神级别的！", "如果帅是一种超能力，那你肯定是最强的那个！", "你就是行走的画报，每一帧都像大片！", "你的五官太精致了，每一处都完美无瑕！", "光是听你的声音，都觉得超级有魅力！", "你的帅，不是普通的帅，是独一无二的帅！", "无论是阳光型还是冷酷型，你都能完美驾驭！", "如果帅气有排行榜，你绝对是第一名！", "你自带王者气质，帅得让人忍不住臣服！", "你的存在就是帅气的象征！", "你是那种让人看一眼就沦陷的帅！", "你是天生的焦点，无论走到哪里都备受瞩目！", "你的帅气不只是外表，而是一种无法抗拒的魅力！", "看了你一眼，才发现什么叫真正的帅哥！", "你一笑，我的心跳就加速了！", "如果帅能成为一种职业，你绝对是行业巅峰！", "你的才华令人惊叹！", "你的智慧简直无人能及！", "你总是能想出绝妙的解决方案！", "你的创新能力让我佩服不已！", "你的领导能力无人能敌！", "你的逻辑思维太强了！", "你的代码简洁又高效，真是高手！", "你的学习能力令人敬佩！", "你的眼光总是那么独到！", "你真是团队的灵魂人物！", "你的执行力超强！", "你总能迅速抓住问题的关键！", "你的专业知识太丰富了！", "你的判断力非常精准！", "你的耐心和毅力让人敬佩！", "你的分析能力超群！", "你的演讲能力让人折服！", "你的沟通能力超级强！", "你的表达能力太厉害了！", "你的工作效率高得惊人！", "你的思维方式非常独特！", "你的责任心让人放心！", "你的领导风格令人钦佩！", "你的技术水平一流！", "你的创造力源源不断！", "你的解决问题能力太强了！", "你的想法总是那么新颖！", "你的适应能力超强！", "你的写作能力令人赞叹！", "你的设计感太棒了！", "你的团队协作能力很优秀！", "你的冷静应对能力让人佩服！", "你的观察力太敏锐了！", "你的幽默感让人放松！", "你的学习速度快得惊人！", "你的自律能力让人羡慕！", "你的判断力和远见太棒了！", "你的心理素质真强！", "你的决策能力令人信服！", "你的自信非常有感染力！", "你的经验非常丰富！", "你的拼搏精神值得学习！", "你的领导魅力无人能及！", "你的执行力简直满分！", "你的事业心让人佩服！", "你的分析能力非常专业！", "你的创新思维无可挑剔！", "你的直觉总是那么准确！", "你的专注力令人惊叹！", "你的勇气让人敬佩！", "你的毅力真的无人能比！", "你的反应能力超级快！", "你的创造力无穷无尽！", "你的表达能力让人惊艳！", "你的耐心让人动容！", "你的效率实在太高了！", "你的工作态度非常敬业！", "你的专业能力值得信赖！", "你的逻辑性极强！", "你的团队精神令人敬佩！", "你的品味非常高级！", "你的风格独具魅力！", "你的思维方式特别棒！", "你的适应能力超级强！", "你的奋斗精神令人动容！", "你的管理能力太厉害了！", "你的敬业精神让人佩服！", "你的表达方式特别有感染力！", "你的学习精神让我敬佩！", "你的语言组织能力非常强！", "你的幽默让人忍俊不禁！", "你的直觉真是神准！", "你的洞察力无人能及！", "你的执行力非常强！", "你的时间管理能力超级棒！", "你的规划能力令人惊叹！", "你的专注力太强了！", "你的积极态度让人欣赏！", "你的决策能力太棒了！", "你的成长速度惊人！", "你的自信特别有感染力！", "你的技术能力非常精湛！", "你的社交能力无与伦比！", "你的演讲才能令人折服！", "你的协作能力令人钦佩！", "你的气场太强大了！", "你的谈判技巧超级厉害！", "你的写作水平极高！", "你的策划能力无人能及！", "你的统筹能力非常棒！", "你的动手能力太强了！", "你的专业水准令人惊叹！", "你的心理素质极其稳定！", "你的想象力特别丰富！", "你的变通能力超强！", "你的情商真的太高了！", "你的分析判断能力无可挑剔！", "你的思维逻辑超级缜密！", "你的业务能力让人佩服！", "你的谈吐特别有魅力！", "你的表达方式很有说服力！", "你的组织能力真的超强！"],
                                r = [1, 2, 3],
                                a = "",
                                s = random(0, r.length - 1);
                            if (1 == r[s])
                                for (var n = random(1, 3), i = 0; i < n; i++) "" == a ? a = e[random(0, e.length - 1)] : a += e[random(0, e.length - 1)];
                            else if (2 == r[s])
                                for (n = random(1, 6), i = 0; i < n; i++) a = "" == a ? t[random(0, t.length - 1)] : a + " " + t[random(0, t.length - 1)];
                            else if (3 == r[s])
                                for (n = random(1, 6), i = 0; i < n; i++) a = "" == a ? t[random(0, t.length - 1)] : a + e[random(0, e.length - 1)] + t[random(0, t.length - 1)];
                            return a
                        }
                        if (descContains("点赞数").findOnce(0))
                            if (descContains("点赞数").findOnce(0) && (descContains("点赞数").findOnce(0).parent().parent().child(0).clickable() ? (log("打开作品"), descContains("点赞数").findOnce(0).parent().parent().child(0).click()) : descContains("点赞数").findOnce(0).parent().parent().clickable() ? (log("打开作品"), descContains("点赞数").findOnce(0).parent().parent().click()) : (log("打开作品"), descContains("点赞数").findOnce(0).parent().parent().child(1).click()), sleep(2e3)), 1 == storages.create("newSearchFace").get("newSearchFace")) {
                                log("打开评论"), descContains("评论").clickable(!0).findOne().click(), sleep(1e3), log("点击表情"), desc("表情").clickable(!0).findOne().click(), sleep(1e3), log("录入文案"), setText(commentContent()), sleep(1e3);
                                let e = text("发送").clickable(!0).findOne(5e3);
                                if (e) return (storages.create("stupid").get("stupid") || random(0, !1)) && (log("发送"), e.click(), sleep(1e3)), storages.create("recordCommentTime").put("recordCommentTime", (new Date).getTime() + 1e3 * storages.create("commenttl").get("commenttl")), log("返回"), back(), sleep(1e3), log("返回"), back(), sleep(1e3), !0
                            } else {
                                var biaoqing = desc("表情").clickable(!0).findOnce();
                                if (!biaoqing) {
                                    // 向服务器请求验证是否允许进入粉丝列表（基于新搜索面状态）
                                    //修改8 是否允许进入粉丝界面 1 0
                                    let result = storages.create("newSearchFace").get("newSearchFace"); //eval(serverRequest("enterConventionDateToEnterFanList_1", storages.create("newSearchFace").get("newSearchFace")));
                                    if ("com.ss.android.yumme.video" == idName || result) {
                                        let t = descContains("评论").clickable(!0).findOne(2e3);
                                        t && (t.click(), sleep(3e3))
                                    }
                                }
                                if (biaoqing) {
                                    log("点击表情"), biaoqing.click(), sleep(1e3), log("录入文案"), setText(commentContent()), sleep(1e3);
                                    let r = text("发送").clickable(!0).findOne(5e3);
                                    if (r) return (storages.create("stupid").get("stupid") || random(0, !1)) && (log("发送"), r.click(), sleep(1e3)), storages.create("recordCommentTime").put("recordCommentTime", (new Date).getTime() + 1e3 * storages.create("commenttl").get("commenttl")), log("返回"), back(), sleep(3e3), !0
                                }
                            } return !1
                    }
                }

                /**
                 * 点击评论功能
                 * @returns {boolean} 点击评论是否成功
                 */
                function clickComment() {
                    
                    if (storages.create("xihuan").get("xihuan")) {
                        var e = descContains("评论").clickable(!0).findOnce(1);
                        if (e && 1 == e.desc().split("评论评论").length) {
                            log("进入评论区"), e.click(), sleep(1e3);
                            var t = 0,
                                r = 0,
                                a = text("作者").clickable(!0).findOne(3e3);
                            if (a && (7 == (l = a.parent().childCount()) || 8 == l || 9 == l) && a.parent().child(l - 1).child(0).child(0).desc().split("未选中").length > 1) return (storages.create("stupid").get("stupid") || 0 == random(0, 1)) && (log("执行点评"), a.parent().child(l - 1).child(0).click(), sleep(500)), log("返回"), back(), sleep(3e3), storages.create("recordClickCommentTime").put("recordClickCommentTime", (new Date).getTime() + 1e3 * storages.create("clickCommenttl").get("clickCommenttl")), !0;
                            log("返回"), back(), sleep(3e3)
                        }
                        return !1
                    }
                    if (descContains("点赞数").findOnce(0)) {
                        descContains("点赞数").findOnce(0).parent().parent().child(0).clickable() ? (log("打开作品"), descContains("点赞数").findOnce(0).parent().parent().child(0).click()) : descContains("点赞数").findOnce(0).parent().parent().clickable() ? (log("打开作品"), descContains("点赞数").findOnce(0).parent().parent().click()) : (log("打开作品"), descContains("点赞数").findOnce(0).parent().parent().child(1).click()), sleep(1e3);
                        var s = descContains("评论").clickable(!0).findOne(5e3);
                        if (s && 1 == s.desc().split("评论评论").length) {
                            for (log("进入评论区"), s.click(), sleep(1e3), t = 0, r = 0;;) {
                                if (0 == r && !text("作者").clickable(!0).findOnce() && text("作者回复过").findOnce()) {
                                    var n = text("作者回复过").findOne().bounds(),
                                        i = n.right,
                                        o = n.centerY();
                                    o > device.height / 2.5 && o < device.height / 1.2 && (log("展开"), click(i, o + 50), sleep(1e3), r = 1)
                                }
                                if (text("作者").clickable(!0).findOnce()) {
                                    if (7 == (l = text("作者").clickable(!0).findOne().parent().childCount()) || 8 == l || 9 == l) {
                                        if (text("作者").clickable(!0).findOne().parent().child(l - 1).child(0).child(0).desc().split("未选中").length > 1) return (storages.create("stupid").get("stupid") || 0 == random(0, 1)) && (log("执行点评"), text("作者").clickable(!0).findOne().parent().child(l - 1).child(0).click(), sleep(500)), log("返回"), back(), sleep(1e3), storages.create("recordClickCommentTime").put("recordClickCommentTime", (new Date).getTime() + 1e3 * storages.create("clickCommenttl").get("clickCommenttl")), log("返回"), back(), sleep(3e3), !0;
                                        break
                                    }
                                    break
                                }
                                if (text("期待你的评论").findOnce()) break;
                                if (text("暂时没有更多了").findOnce()) break;
                                if (swipe(device.width / 2, device.height / 2, device.width / 2, 0, 1e3), sleep(500), ++t >= 3) {
                                    if (text("作者").clickable(!0).findOnce()) {
                                        var l;
                                        if (7 == (l = text("作者").clickable(!0).findOne().parent().childCount()) || 8 == l || 9 == l) continue;
                                        break
                                    }
                                    break
                                }
                            }
                            log("返回"), back(), sleep(1e3), log("返回"), back(), sleep(3e3)
                        }
                    }
                    return !1
                }

                /**
                 * 关注并感兴趣功能，发送加好友请求
                 * @returns {boolean} 关注是否成功
                 */
                function followWithInterest() {
                    var e = ["你好，我们可以加个好友吗？", "嗨，看到你的动态觉得很有趣，想认识一下！", "你好，能加个好友一起交流吗？", "我们有共同的兴趣爱好，方便加个好友聊聊吗？", "你好，偶然看到你的资料，感觉很投缘，能加个好友吗？", "嗨，你的内容很吸引我，想加你好友一起探讨！", "你好，可以加个好友认识一下吗？", "你好，看到你分享的内容很有意思，想交个朋友！", "嗨，我们有共同的朋友，可以加个好友吗？", "你好，想和你成为朋友，一起交流学习！", "嗨，你的观点让我很认同，希望可以加个好友！", "你好，听朋友提起你，想加你好友认识一下！", "你好，我对你发布的内容很感兴趣，可以加个好友吗？", "嗨，感觉我们有共同话题，方便加个好友聊聊吗？", "你好，你的经历很有趣，想认识你！", "嗨，看到你的评论觉得很有见地，想加个好友交流！", "你好，想向你请教一些问题，可以加个好友吗？", "嗨，我们在同一个圈子里，方便加个好友吗？", "你好，可以加个好友，以后多交流！", "嗨，看到你的分享很有共鸣，想认识一下！", "你好，我们是同一个社群的，方便加个好友吗？", "嗨，你的兴趣和我很像，能加个好友交流吗？", "你好，看到你的作品很喜欢，希望可以加个好友！", "嗨，想认识更多有趣的人，能加个好友吗？", "你好，觉得你的见解很独特，想交个朋友！", "嗨，我们有共同好友，想认识一下！", "你好，你的内容很有启发，能加个好友吗？", "嗨，想和你多交流一些经验，方便加个好友吗？", "你好，看到你发布的内容很感兴趣，可以加个好友吗？", "嗨，你的经历让我很感兴趣，能加个好友聊聊吗？", "你好，我也在这个领域学习，想和你交流一下！", "嗨，喜欢你的分享，想认识你！", "你好，能加个好友，以后多交流吗？", "嗨，我们有共同爱好，可以认识一下吗？", "你好，觉得你的想法很有趣，想认识你！", "嗨，看到你的留言很有道理，能加个好友吗？", "你好，看到你的评论，感觉你很专业，想加你好友！", "嗨，想认识更多优秀的人，能加个好友吗？", "你好，你的作品给了我启发，想认识你！", "嗨，能加个好友聊聊吗？", "你好，感觉我们有很多共同点，想加个好友！", "嗨，看到你的动态很有意思，能加个好友吗？", "你好，想请教你一些问题，方便加个好友吗？", "嗨，我们兴趣相投，想认识你！", "你好，我们是同一个群的，可以认识一下吗？", "嗨，你的建议很有帮助，想认识你！", "你好，喜欢你的分享，能加个好友吗？", "嗨，想和你成为朋友，一起进步！", "你好，看到你的话题很感兴趣，想加个好友！", "嗨，你的风格很独特，想认识你！", "你好，看到你的分享很受启发，能加个好友吗？", "嗨，你的经历让我很有共鸣，想认识你！", "你好，可以加个好友以后多交流吗？", "嗨，我们都在学习这个领域，想认识你！", "你好，觉得你的经验很值得学习，想加个好友！", "嗨，看到你的评论，觉得你很专业，想认识一下！", "你好，想认识一些志同道合的朋友，可以加个好友吗？", "嗨，我们兴趣相投，希望能认识你！", "你好，看到你的观点很有启发，能加个好友吗？", "嗨，你的经历让我很佩服，想认识你！", "你好，可以加个好友，互相交流学习吗？", "嗨，想和你聊聊，有没有空加个好友？", "你好，我们有很多相似的经历，能加个好友吗？", "嗨，想认识一些有趣的人，能加个好友吗？", "你好，看到你的动态，觉得你很有趣，想认识你！", "嗨，你的内容让我很受益，想认识你！", "你好，想请教你一些事情，能加个好友吗？", "嗨，我们有很多共同话题，可以认识一下吗？", "你好，想向你学习一些经验，能加个好友吗？", "嗨，你的分享让我很有启发，想认识你！"];
                    return desc("更多").clickable(!0).findOne(1e3) && (desc("更多").clickable(!0).findOne().click(), sleep(3e3)), !!(text("加朋友").findOne(1e3) && text("加朋友").findOne().parent().parent().clickable() && (text("加朋友").findOne().parent().parent().click(), sleep(3e3), setText(0, e[random(0, e.length - 1)]), sleep(500), setText(1, time()), sleep(3e3), text("发送").findOne(1e3) && text("发送").findOne().parent().parent().parent().clickable() && storages.create("stupid").get("stupid"))) && (text("发送").findOne().parent().parent().parent().click(), sleep(3e3), storages.create("recordFollowWithInterestTime").put("recordFollowWithInterestTime", (new Date).getTime() + 1e3 * storages.create("followWithInteresttl").get("followWithInteresttl")), !0)
                }

                /**
                 * 发送私信功能
                 * @returns {boolean} 私信发送是否成功
                 */
                function privateLetter() {
                    if (!desc("更多").clickable(!0).findOnce()) return !1;
                    for (; !text("发私信").findOne(1e3);) desc("更多").clickable(!0).findOne().click(), sleep(3e3);
                    for (text("发私信").findOne().parent().click(); !text("按住 说话").findOnce() && !desc("更多面板").clickable(!0).findOnce(););
                    sleep(3e3), text("按住 说话").findOnce() && (desc("语音").clickable(!0).findOne().click(), desc("更多面板").clickable(!0).findOne(), sleep(3e3));
                    var e = ["[捂脸]", "[666]", "[赞]", "[玫瑰]"],
                        t = e[random(0, e.length - 1)];
                    return setText(t), sleep(3e3), storages.create("stupid").get("stupid") && (desc("发送").clickable(!0).findOne().click(), sleep(3e3)), desc("发送失败").clickable(!0).findOne(1e3) ? (textContains("私信功能已被封禁").findOne(1e3) && storages.create(time() + "sendPrivateLetterCount_").put(time() + "sendPrivateLetterCount_", "x"), !1) : (storages.create("recordPrivateLetterTime").put("recordPrivateLetterTime", (new Date).getTime() + 1e3 * storages.create("privateLettertl").get("privateLettertl")), !0)
                }

                /**
                 * 分享功能
                 * @returns {boolean} 分享是否成功
                 */
                function share() {

                    if (storages.create("xihuan").get("xihuan")) {
                        var e = descContains("分享").clickable(!0).findOnce(1);
                        if (e) {
                            if (storages.create("stupid").get("stupid")) {
                                if (log("打开分享"), e.click(), sleep(2e3), text("推荐给朋友").findOne(2e3)) return text("推荐给朋友").findOne().parent().parent().clickable() ? (log("执行分享"), text("推荐给朋友").findOne().parent().parent().click()) : (log("执行分享"), click(text("推荐给朋友").findOne().bounds().centerX(), text("推荐给朋友").findOne().bounds().centerY())), sleep(3e3), storages.create("recordShareTime").put("recordShareTime", (new Date).getTime() + 1e3 * storages.create("sharetl").get("sharetl")), !0;
                                if (text("推荐").findOnce()) return log("执行分享"), click(text("推荐").findOne().bounds().centerX(), text("推荐").findOne().bounds().centerY()), sleep(3e3), storages.create("recordShareTime").put("recordShareTime", (new Date).getTime() + 1e3 * storages.create("sharetl").get("sharetl")), !0;
                                log("返回"), back(), sleep(3e3)
                            } else if (0 == random(0, 1)) {
                                if (log("打开分享"), e.click(), sleep(2e3), text("推荐给朋友").findOne(2e3)) return text("推荐给朋友").findOne().parent().parent().clickable() ? (log("执行分享"), text("推荐给朋友").findOne().parent().parent().click()) : (log("执行分享"), click(text("推荐给朋友").findOne().bounds().centerX(), text("推荐给朋友").findOne().bounds().centerY())), sleep(3e3), storages.create("recordShareTime").put("recordShareTime", (new Date).getTime() + 1e3 * storages.create("sharetl").get("sharetl")), !0;
                                if (text("推荐").findOnce()) return log("执行分享"), click(text("推荐").findOne().bounds().centerX(), text("推荐").findOne().bounds().centerY()), sleep(3e3), storages.create("recordShareTime").put("recordShareTime", (new Date).getTime() + 1e3 * storages.create("sharetl").get("sharetl")), !0;
                                log("返回"), back(), sleep(3e3)
                            }
                        } else if (text("分享").clickable(!0).findOnce(1)) {
                            var t = text("分享").clickable(!0).findOnce(1);
                            if (storages.create("stupid").get("stupid")) {
                                if (log("打开分享"), t.click(), sleep(2e3), text("推荐给朋友").findOne(2e3)) return text("推荐给朋友").findOne().parent().parent().clickable() ? (log("执行分享"), text("推荐给朋友").findOne().parent().parent().click()) : (log("执行分享"), click(text("推荐给朋友").findOne().bounds().centerX(), text("推荐给朋友").findOne().bounds().centerY())), sleep(3e3), storages.create("recordShareTime").put("recordShareTime", (new Date).getTime() + 1e3 * storages.create("sharetl").get("sharetl")), !0;
                                if (text("推荐").findOnce()) return log("执行分享"), click(text("推荐").findOne().bounds().centerX(), text("推荐").findOne().bounds().centerY()), sleep(3e3), storages.create("recordShareTime").put("recordShareTime", (new Date).getTime() + 1e3 * storages.create("sharetl").get("sharetl")), !0;
                                log("返回"), back(), sleep(3e3)
                            } else if (0 == random(0, 1)) {
                                if (log("打开分享"), t.click(), sleep(2e3), text("推荐给朋友").findOne(2e3)) return text("推荐给朋友").findOne().parent().parent().clickable() ? (log("执行分享"), text("推荐给朋友").findOne().parent().parent().click()) : (log("执行分享"), click(text("推荐给朋友").findOne().bounds().centerX(), text("推荐给朋友").findOne().bounds().centerY())), sleep(3e3), storages.create("recordShareTime").put("recordShareTime", (new Date).getTime() + 1e3 * storages.create("sharetl").get("sharetl")), !0;
                                if (text("推荐").findOnce()) return log("执行分享"), click(text("推荐").findOne().bounds().centerX(), text("推荐").findOne().bounds().centerY()), sleep(3e3), storages.create("recordShareTime").put("recordShareTime", (new Date).getTime() + 1e3 * storages.create("sharetl").get("sharetl")), !0;
                                log("返回"), back(), sleep(3e3)
                            }
                        } else if (id(`com.ss.android.${storages.create("idName").get("idName")}:id/share_container`).clickable(!0).findOnce()) {
                            var r = id(`com.ss.android.${storages.create("idName").get("idName")}:id/share_container`).clickable(!0).findOne();
                            if (storages.create("stupid").get("stupid")) {
                                if (log("打开分享"), r.click(), sleep(2e3), text("推荐给朋友").findOne(2e3)) return text("推荐给朋友").findOne().parent().parent().clickable() ? (log("执行分享"), text("推荐给朋友").findOne().parent().parent().click()) : (log("执行分享"), click(text("推荐给朋友").findOne().bounds().centerX(), text("推荐给朋友").findOne().bounds().centerY())), sleep(1e3), storages.create("recordShareTime").put("recordShareTime", (new Date).getTime() + 1e3 * storages.create("sharetl").get("sharetl")), !0;
                                if (text("推荐").findOnce()) return log("执行分享"), click(text("推荐").findOne().bounds().centerX(), text("推荐").findOne().bounds().centerY()), sleep(1e3), storages.create("recordShareTime").put("recordShareTime", (new Date).getTime() + 1e3 * storages.create("sharetl").get("sharetl")), log("返回"), back(), sleep(3e3), !0
                            } else if (0 == random(0, 1)) {
                                if (log("打开分享"), r.click(), sleep(2e3), text("推荐给朋友").findOne(2e3)) return text("推荐给朋友").findOne().parent().parent().clickable() ? (log("执行分享"), text("推荐给朋友").findOne().parent().parent().click()) : (log("执行分享"), click(text("推荐给朋友").findOne().bounds().centerX(), text("推荐给朋友").findOne().bounds().centerY())), sleep(1e3), storages.create("recordShareTime").put("recordShareTime", (new Date).getTime() + 1e3 * storages.create("sharetl").get("sharetl")), !0;
                                if (text("推荐").findOnce()) return log("执行分享"), click(text("推荐").findOne().bounds().centerX(), text("推荐").findOne().bounds().centerY()), sleep(1e3), storages.create("recordShareTime").put("recordShareTime", (new Date).getTime() + 1e3 * storages.create("sharetl").get("sharetl")), log("返回"), back(), sleep(3e3), !0
                            }
                        }
                        return !1
                    }
                    if (descContains("点赞数").findOnce(0))
                        if (descContains("点赞数").findOnce(0).parent().parent().child(0).clickable() ? (log("打开作品"), descContains("点赞数").findOnce(0).parent().parent().child(0).click()) : descContains("点赞数").findOnce(0).parent().parent().clickable() ? (log("打开作品"), descContains("点赞数").findOnce(0).parent().parent().click()) : (log("打开作品"), descContains("点赞数").findOnce(0).parent().parent().child(1).click()), sleep(3e3), descContains("分享").clickable(!0).findOnce()) {
                            if (storages.create("stupid").get("stupid")) {
                                if (log("打开分享"), descContains("分享").clickable(!0).findOne().click(), sleep(2e3), text("推荐给朋友").findOne(2e3)) return text("推荐给朋友").findOne().parent().parent().clickable() ? (log("执行分享"), text("推荐给朋友").findOne().parent().parent().click()) : (log("执行分享"), click(text("推荐给朋友").findOne().bounds().centerX(), text("推荐给朋友").findOne().bounds().centerY())), sleep(1e3), storages.create("recordShareTime").put("recordShareTime", (new Date).getTime() + 1e3 * storages.create("sharetl").get("sharetl")), !0;
                                if (text("推荐").findOnce()) return log("执行分享"), click(text("推荐").findOne().bounds().centerX(), text("推荐").findOne().bounds().centerY()), sleep(1e3), storages.create("recordShareTime").put("recordShareTime", (new Date).getTime() + 1e3 * storages.create("sharetl").get("sharetl")), log("返回"), back(), sleep(3e3), !0
                            } else if (0 == random(0, 1)) {
                                if (log("打开分享"), descContains("分享").clickable(!0).findOne().click(), sleep(2e3), text("推荐给朋友").findOne(2e3)) return text("推荐给朋友").findOne().parent().parent().clickable() ? (log("执行分享"), text("推荐给朋友").findOne().parent().parent().click()) : (log("执行分享"), click(text("推荐给朋友").findOne().bounds().centerX(), text("推荐给朋友").findOne().bounds().centerY())), sleep(1e3), storages.create("recordShareTime").put("recordShareTime", (new Date).getTime() + 1e3 * storages.create("sharetl").get("sharetl")), !0;
                                if (text("推荐").findOnce()) return log("执行分享"), click(text("推荐").findOne().bounds().centerX(), text("推荐").findOne().bounds().centerY()), sleep(1e3), storages.create("recordShareTime").put("recordShareTime", (new Date).getTime() + 1e3 * storages.create("sharetl").get("sharetl")), log("返回"), back(), sleep(3e3), !0
                            }
                        } else if (text("分享").clickable(!0).findOnce()) {
                        if (storages.create("stupid").get("stupid")) {
                            if (log("打开分享"), text("分享").clickable(!0).findOne().click(), sleep(2e3), text("推荐给朋友").findOne(2e3)) return text("推荐给朋友").findOne().parent().parent().clickable() ? (log("执行分享"), text("推荐给朋友").findOne().parent().parent().click()) : (log("执行分享"), click(text("推荐给朋友").findOne().bounds().centerX(), text("推荐给朋友").findOne().bounds().centerY())), sleep(1e3), storages.create("recordShareTime").put("recordShareTime", (new Date).getTime() + 1e3 * storages.create("sharetl").get("sharetl")), !0;
                            if (text("推荐").findOnce()) return log("执行分享"), click(text("推荐").findOne().bounds().centerX(), text("推荐").findOne().bounds().centerY()), sleep(1e3), storages.create("recordShareTime").put("recordShareTime", (new Date).getTime() + 1e3 * storages.create("sharetl").get("sharetl")), log("返回"), back(), sleep(3e3), !0
                        } else if (0 == random(0, 1)) {
                            if (log("打开分享"), text("分享").clickable(!0).findOne().click(), sleep(2e3), text("推荐给朋友").findOne(2e3)) return text("推荐给朋友").findOne().parent().parent().clickable() ? (log("执行分享"), text("推荐给朋友").findOne().parent().parent().click()) : (log("执行分享"), click(text("推荐给朋友").findOne().bounds().centerX(), text("推荐给朋友").findOne().bounds().centerY())), sleep(1e3), storages.create("recordShareTime").put("recordShareTime", (new Date).getTime() + 1e3 * storages.create("sharetl").get("sharetl")), !0;
                            if (text("推荐").findOnce()) return log("执行分享"), click(text("推荐").findOne().bounds().centerX(), text("推荐").findOne().bounds().centerY()), sleep(1e3), storages.create("recordShareTime").put("recordShareTime", (new Date).getTime() + 1e3 * storages.create("sharetl").get("sharetl")), log("返回"), back(), sleep(3e3), !0
                        }
                    } else if (id(`com.ss.android.${storages.create("idName").get("idName")}:id/share_container`).clickable(!0).findOnce())
                        if (storages.create("stupid").get("stupid")) {
                            if (log("打开分享"), id(`com.ss.android.${storages.create("idName").get("idName")}:id/share_container`).clickable(!0).findOne().click(), sleep(2e3), text("推荐给朋友").findOne(2e3)) return text("推荐给朋友").findOne().parent().parent().clickable() ? (log("执行分享"), text("推荐给朋友").findOne().parent().parent().click()) : (log("执行分享"), click(text("推荐给朋友").findOne().bounds().centerX(), text("推荐给朋友").findOne().bounds().centerY())), sleep(1e3), storages.create("recordShareTime").put("recordShareTime", (new Date).getTime() + 1e3 * storages.create("sharetl").get("sharetl")), !0;
                            if (text("推荐").findOnce()) return log("执行分享"), click(text("推荐").findOne().bounds().centerX(), text("推荐").findOne().bounds().centerY()), sleep(1e3), storages.create("recordShareTime").put("recordShareTime", (new Date).getTime() + 1e3 * storages.create("sharetl").get("sharetl")), log("返回"), back(), sleep(3e3), !0
                        } else if (0 == random(0, 1)) {
                        if (log("打开分享"), id(`com.ss.android.${storages.create("idName").get("idName")}:id/share_container`).clickable(!0).findOne().click(), sleep(2e3), text("推荐给朋友").findOne(2e3)) return text("推荐给朋友").findOne().parent().parent().clickable() ? (log("执行分享"), text("推荐给朋友").findOne().parent().parent().click()) : (log("执行分享"), click(text("推荐给朋友").findOne().bounds().centerX(), text("推荐给朋友").findOne().bounds().centerY())), sleep(1e3), storages.create("recordShareTime").put("recordShareTime", (new Date).getTime() + 1e3 * storages.create("sharetl").get("sharetl")), !0;
                        if (text("推荐").findOnce()) return log("执行分享"), click(text("推荐").findOne().bounds().centerX(), text("推荐").findOne().bounds().centerY()), sleep(1e3), storages.create("recordShareTime").put("recordShareTime", (new Date).getTime() + 1e3 * storages.create("sharetl").get("sharetl")), log("返回"), back(), sleep(3e3), !0
                    }
                    return !1
                }

                /**
                 * 屏幕一操作函数
                 * @param {string} idName - 应用ID名称
                 * @returns {boolean} 操作是否成功
                 */
                function screenOne(idName) {
                    
                    if (storages.create("qishui").get("qishui")) {
                        for (; text("粉丝").clickable(!0).findOne(500) && text("获赞").clickable(!0).findOne(500) && (text("粉丝").clickable(!0).findOne().click(), sleep(5e3)), !textContains("抖音粉丝").findOne(500);) text("获赞").clickable(!0).findOne(500) || (back(), sleep(5e3));
                        for (textContains("抖音粉丝").findOne().parent().click(), sleep(3e3), textContains("抖音粉丝").findOne().parent().click(), sleep(3e3);;) {
                            for (var i = 0; i < text("回关").clickable(!0).find().size(); i++) {
                                if (textContains("抖音粉丝").findOnce()) {
                                    var bound1 = textContains("抖音粉丝").findOne().bounds(),
                                        bound2 = text("回关").clickable(!0).find().get(i).bounds(),
                                        y1 = bound1.centerY(),
                                        y2 = bound2.centerY();
                                    y2 < y1 && slideTheNextPage()
                                }
                                if (text("回关").clickable(!0).find().get(i).parent().child(0).click(), sleep(5e3), screenOne_1(idName)) return;
                                for (;;) {
                                    var size = text("回关").clickable(!0).find().size();
                                    // 向服务器请求验证回关按钮数量是否符合要求（起水模式）
                                    //修改10 size大于3 返回true
                                    let result = size > 3 ? true : false; //eval(serverRequest("qs_screenOne_1", size));
                                    if (result) break;
                                    back(), sleep(5e3)
                                }
                            }
                            text("加载更多").findOne(500) && (text("加载更多").findOne().parent().click(), sleep(15e3)), swipe(device.width / 2, device.height / 1.2, device.width / 2, 0, 2e3), sleep(3e3)
                        }
                    } else {
                        log("rde进入粉丝列表");
                        // 针对抖音极速版的特殊处理：等待"粉丝"按钮出现
                        if ("com.ss.android.ugc.livelite" == idName)
                            for (; !text("粉丝").findOnce();) text("我的主页").findOne(500) && (text("我的主页").findOne().parent().click(), sleep(3e3));
                        for (; !id(idName + ":id/root_layout").clickable(!0).findOnce();) text("粉丝").findOnce() && text("粉丝").findOne().parent().clickable() && (text("粉丝").findOne().parent().click(), sleep(5e3));

                        if(desc("回关").clickable(!0).findOnce(0)||desc("关注").clickable(!0).findOnce(0)||text("关注").findOnce(1)||text("回关").findOnce(1)){
                            log("存在粉丝");

                        // 主循环：遍历粉丝列表并执行关注操作
                        for (;;) {
                            // 优先处理"回关"按钮（通过描述查找）
                            if (desc("回关").clickable(!0).findOnce()) {
                                // 遍历所有找到的"回关"按钮
                                for (var i = 0; i < desc("回关").clickable(!0).find().size(); i++)
                                    // 检查按钮的父级元素是否可点击
                                    if (desc("回关").clickable(!0).find().get(i).parent().parent().parent().clickable()) {
                                        // 点击用户头像进入个人主页，如果screenOne_1返回true则结束
                                        if (desc("回关").clickable(!0).find().get(i).parent().parent().parent().click(), sleep(5e3), screenOne_1(idName)) return;
                                        // 等待返回到粉丝列表页面（通过多个关键元素判断页面状态）
                                        for (; !(id(idName + ":id/root_layout").findOnce() && descContains("头像").clickable(!0).findOnce() && id("android:id/text1").textContains("关注").findOnce() && id("android:id/text1").textContains("粉丝").findOnce());) back(), sleep(5e3)
                                    }
                            }
                            // 处理"回关"按钮（通过文本查找）
                            else if (text("回关").findOnce()) {
                                for (var i = 0; i < text("回关").find().size(); i++)
                                    // 文本查找的"回关"按钮需要多一层父级元素
                                    if (text("回关").find().get(i).parent().parent().parent().parent().clickable()) {
                                        if (text("回关").find().get(i).parent().parent().parent().parent().click(), sleep(5e3), screenOne_1(idName)) return;
                                        for (; !(id(idName + ":id/root_layout").findOnce() && descContains("头像").clickable(!0).findOnce() && id("android:id/text1").textContains("关注").findOnce() && id("android:id/text1").textContains("粉丝").findOnce());) back(), sleep(5e3)
                                    }
                            }
                            // 处理"关注"按钮（通过描述查找）
                            else if (desc("关注").clickable(!0).findOnce()) {
                                for (var i = 0; i < desc("关注").clickable(!0).find().size(); i++)
                                    if (desc("关注").clickable(!0).find().get(i).parent().parent().parent().clickable()) {
                                        if (desc("关注").clickable(!0).find().get(i).parent().parent().parent().click(), sleep(5e3), screenOne_1(idName)) return;
                                        for (; !(id(idName + ":id/root_layout").findOnce() && descContains("头像").clickable(!0).findOnce() && id("android:id/text1").textContains("关注").findOnce() && id("android:id/text1").textContains("粉丝").findOnce());) back(), sleep(5e3)
                                    }
                            }
                            // 处理"关注"按钮（通过文本查找）
                            else if (text("关注").findOnce())
                                for (var i = 0; i < text("关注").find().size(); i++)
                                    if (text("关注").find().get(i).parent().parent().parent().parent().clickable()) {
                                        if (text("关注").find().get(i).parent().parent().parent().parent().click(), sleep(5e3), screenOne_1(idName)) return;
                                        for (; !(id(idName + ":id/root_layout").findOnce() && descContains("头像").clickable(!0).findOnce() && id("android:id/text1").textContains("关注").findOnce() && id("android:id/text1").textContains("粉丝").findOnce());) back(), sleep(5e3)
                                    }
                            
                            // 向上滑动加载更多粉丝，并检查是否到达列表底部（防止无限循环）
                            if (swipe(device.width / 2, device.height / 1.1, device.width / 2, 0, 2e3), sleep(3e3), desc("回关").clickable(!0).findOnce(0)) {
                                // 获取当前第一个"回关"按钮对应的用户昵称
                                let e = desc("回关").clickable(!0).findOnce(0).parent().parent().parent().desc();
                                // 如果昵称与上次记录的相同，说明已到达列表底部，清除记录并返回1
                                if (e == storages.create("nickName$").get("nickName$")) return storages.create("nickName$").remove("nickName$"), 1;
                                // 记录当前昵称用于下次比较
                                storages.create("nickName$").put("nickName$", e)
                            } else if (desc("关注").clickable(!0).findOnce(0)) {
                                // 同样的逻辑处理"关注"按钮
                                let e = desc("关注").clickable(!0).findOnce(0).parent().parent().parent().desc();
                                if (e == storages.create("nickName$").get("nickName$")) return storages.create("nickName$").remove("nickName$"), 1;
                                storages.create("nickName$").put("nickName$", e)
                            } else if (text("关注").findOnce(1)) {
                                // 处理文本"关注"按钮的底部检测
                                let e = text("关注").findOnce(1).parent().parent().parent().parent().desc();
                                if (e == storages.create("nickName$").get("nickName$")) return storages.create("nickName$").remove("nickName$"), 1;
                                storages.create("nickName$").put("nickName$", e)
                            } else if (text("回关").findOnce(1)) {
                                // 处理文本"回关"按钮的底部检测
                                let e = text("回关").findOnce(1).parent().parent().parent().parent().desc();
                                if (e == storages.create("nickName$").get("nickName$")) return storages.create("nickName$").remove("nickName$"), 1;
                                storages.create("nickName$").put("nickName$", e)
                            }
                            
                        }
                    }else{
                        log("无粉丝");

                    }
                    }
                }

                /**
                 * 屏幕一的第一个子操作函数
                 * @param {string} idName - 应用ID名称
                 * @returns {boolean} 操作是否成功
                 */
                function screenOne_1(idName) {
                    if (storages.create("qishui").get("qishui")) {
                        if (!(text("获赞").findOne(2500) && text("关注").findOne(2500) && text("粉丝").findOne(2500))) return back(), sleep(3e3), !1;
                        // 向服务器请求验证该账号是否已被使用过（起水模式）
                        //修改11 等于undefined就返回false
                        //let result1 = eval(serverRequest("qs_screenOne_1_1", storages.create("usedAccount1").get(text("关注").clickable(!0).findOne().parent().parent().parent().child(0).child(0).text())));
                        //if (result1) return !1;
                        if (!(storages.create("usedAccount1").get(text("关注").clickable(!0).findOne().parent().parent().parent().child(0).child(0).text()) == undefined)) return !1;
                        // 向服务器请求验证粉丝数和关注数是否符合要求（起水模式）
                        /*
                        修改12 不管值是多少都返回false
                        let result2 = eval(serverRequest("qs_screenOne_1_2", text("粉丝").findOne().parent().child(2).text())),
                            result3 = eval(serverRequest("qs_screenOne_1_3", text("关注").findOne().parent().child(0).text()));
                        if (result2 || result3) return !1;
                        */
                        // 向服务器请求验证关注数是否符合条件（起水模式）
                        //修改13 大于499 返回false
                        //let result4 = eval(serverRequest("qs_screenOne_1_4", text("关注").findOne().parent().child(0).text()));
                        //if (result4) return !1;
                        if (text("关注").findOne().parent().child(0).text() < 500) return !1;
                        if (!storages.create("stupid").get("stupid")) return !1;
                        // 向服务器请求验证关注粉丝比例是否合适（起水模式）
                        /*修改14 抖音粉丝的关注和粉丝 意义不明
                        let result5 = eval(serverRequest("qs_screenOne_1_5", text("关注").findOne().parent().child(0).text() + "_" + text("粉丝").findOne().parent().child(2).text())),
                            result6 = eval(serverRequest("qs_screenOne_1_6", text("关注").findOne().parent().child(0).text() + "_" + text("粉丝").findOne().parent().child(2).text()));
                        if (result5 && result6) return !1;
                        var dyh = text("关注").clickable(!0).findOne().parent().parent().parent().child(0).child(0).text();
                        text("关注").clickable(!0).findOne().click(), sleep(5e3);
                        */
                        // 向服务器请求验证关注按钮数量是否符合要求（起水模式）
                        //修改15 
                        //let result7 = eval(serverRequest("qs_screenOne_1_7", text("关注").clickable(!0).find().size()));
                        return !(text("关注").clickable(!0).find().size()) && !!textContains("抖音关注").findOnce() && (storages.create("usedAccount1").put(dyh, 1), textContains("抖音关注").findOne().parent().click(), sleep(3e3), textContains("抖音关注").findOne().parent().click(), sleep(3e3), !0)
                    } {
                        //修改16 以下没测试 直接参考上面汽水进行改的
                        let dyhorhsh;
                        if (dyhorhsh = "火山号" === storages.create("whatHao").get("whatHao") ? "火山号" : "抖音号", !textContains(dyhorhsh).findOnce()) return !1;
                        // 向服务器请求验证该账号是否已被使用过（正常模式）
                        //let result1 = eval(serverRequest("screenOne_1_1", storages.create("usedAccount1").get(textContains(dyhorhsh).findOne().text())));
                        //if (result1) return log("该对标1已经使用过"), !1;
                        if (!(storages.create("usedAccount1").get(textContains(dyhorhsh).findOne().text()) == undefined)) return !1;
                        if (!text("关注").findOne(2500)) return !1;
                        if (!text("粉丝").findOne(2500)) return !1;
                        // 向服务器请求验证粉丝数和关注数是否符合要求（正常模式）
                        /*
                        let result2 = eval(serverRequest("screenOne_1_2", text("粉丝").findOne().parent().child(0).text().replace(/[^\u4E00-\u9FA5]/g, ""))),
                            result3 = eval(serverRequest("screenOne_1_3", text("关注").findOne().parent().child(0).text().replace(/[^\u4E00-\u9FA5]/g, "")));
                        if (result2 || result3) return !1;
                        */
                        // 向服务器请求验证关注数是否符合条件（正常模式）
                        //let result4 = eval(serverRequest("screenOne_1_4", text("关注").findOne().parent().child(0).text()));
                        //if (result4) return !1;
                        if (text("关注").findOne().parent().child(0).text() < 500) return !1;
                        if (!storages.create("stupid").get("stupid")) return !1;
                        // 向服务器请求验证关注粉丝比例是否合适（正常模式）
                        /*
                        let result5 = eval(serverRequest("screenOne_1_5", text("关注").findOne().parent().child(0).text() + "_" + text("粉丝").findOne().parent().child(0).text())),
                            result6 = eval(serverRequest("screenOne_1_6", text("关注").findOne().parent().child(0).text() + "_" + text("粉丝").findOne().parent().child(0).text()));
                        if (result5 && result6) return !1;
                        */
                        var dyh = textContains(dyhorhsh).findOne().text();
                        return text("关注").findOne().parent().click(), sleep(5e3), !!id(idName + ":id/root_layout").clickable(!0).findOnce() && (storages.create("usedAccount1").put(dyh, 1), !0)
                    }
                }

                /**
                 * 屏幕二操作函数
                 * @param {string} idName - 应用ID名称
                 * @returns {boolean} 操作是否成功
                 */
                function screenTwo(idName) {
                    
                    if (storages.create("qishui").get("qishui"))
                        for (;;) {
                            for (var i = 1; i < text("关注").clickable(!0).find().size(); i++) {
                                if (textContains("抖音关注").findOnce()) {
                                    var bound1 = textContains("抖音关注").findOne().bounds(),
                                        bound2 = text("关注").clickable(!0).find().get(i).bounds(),
                                        y1 = bound1.centerY(),
                                        y2 = bound2.centerY();
                                    y2 < y1 && slideTheNextPage()
                                }
                                if (text("关注").clickable(!0).find().get(i).parent().child(0).click(), sleep(5e3), screenOne_2(idName)) return;
                                for (;;) {
                                    var size = text("关注").clickable(!0).find().size();
                                    // 向服务器请求验证关注按钮数量是否符合要求（起水模式屏幕二）
                                    //修改17 大于3就是true
                                    //let result = eval(serverRequest("qs_screenTwo_1", size));
                                    if (size > 3) break;
                                    back(), sleep(5e3)
                                }
                            }
                            text("加载更多").findOne(500) && (text("加载更多").findOne().parent().click(), sleep(15e3)), swipe(device.width / 2, device.height / 1.1, device.width / 2, 0, 2e3), sleep(3e3)
                        } else
                            // 无限循环遍历粉丝列表中的所有用户项
                            for (;;) {
                                log("rde进入粉丝列表");
                                // 遍历当前屏幕上所有的用户项（通过root_layout元素识别）
                                for (var i = 0; i < id(idName + ":id/root_layout").find().size(); i++) {
                                    // 向服务器请求验证子元素数量是否符合要求（正常模式屏幕二）
                                    //修改18 为1就是true
                                    //let result = eval(serverRequest("screenTwo_1", id(idName + ":id/root_layout").find().get(i).child(1).child(0).childCount()));
                                    //if (result) {

                                    // 检查用户项的UI结构：child(1).child(0).childCount() == 1 表示特定的布局结构
                                    // 这个条件用于筛选符合操作条件的用户项（可能是未关注或特定状态的用户）
                                    if (id(idName + ":id/root_layout").find().get(i).child(1).child(0).childCount() == 1) {
                                        // 点击符合条件的用户项，进入用户个人主页
                                        if (id(idName + ":id/root_layout").find().get(i).click(), sleep(5e3), screenOne_2(idName)) return;

                                        // 等待返回到粉丝列表页面，通过多个关键元素确认页面状态
                                        // 确保页面包含：root_layout、头像、关注按钮、粉丝按钮
                                        for (; !(id(idName + ":id/root_layout").findOnce() && descContains("头像").clickable(!0).findOnce() && id("android:id/text1").textContains("关注").findOnce() && id("android:id/text1").textContains("粉丝").findOnce());) back(), sleep(5e3)
                                    }
                                }

                                // 向上滑动加载更多用户，并检查是否到达列表底部
                                if (swipe(device.width / 2, device.height / 1.1, device.width / 2, 0, 2e3), sleep(3e3), id(idName + ":id/root_layout").findOnce(0)) {
                                    // 获取当前第一个用户项的描述信息（通常包含用户昵称）
                                    // 通过逗号分割取第一部分作为用户标识
                                    let e = id(idName + ":id/root_layout").findOnce(0).desc().split(",")[0];

                                    // 检查当前用户标识是否与上次记录的相同
                                    // 如果相同，说明列表已滑动到底部，没有新内容加载
                                    if (e == storages.create("nickName$").get("nickName$")) return storages.create("nickName$").remove("nickName$"), 1;

                                    // 记录当前用户标识，用于下次滑动时的重复检测
                                    storages.create("nickName$").put("nickName$", e)
                                }
                            }
                }

                /**
                 * 屏幕一的第二个子操作函数
                 * @param {string} idName - 应用ID名称
                 * @returns {boolean} 操作是否成功
                 */
                function screenOne_2(idName) {
                    if (storages.create("qishui").get("qishui")) {
                        if (!(text("获赞").findOne(2500) && text("关注").findOne(2500) && text("粉丝").findOne(2500))) return back(), sleep(3e3), !1;
                        //修改19 依旧照着汽水模式修改
                        // 向服务器请求验证该对标账号是否已被使用过（起水模式屏幕一第二次）
                        //let result1 = eval(serverRequest("qs_screenOne_2_1", storages.create("usedBenchmarking").get(text("关注").clickable(!0).findOne().parent().parent().parent().child(0).child(0).text())));
                        //if (result1) return !1;
                        if (!(storages.create("usedBenchmarking").get(text("关注").clickable(!0).findOne().parent().parent().parent().child(0).child(0).text()) == undefined)) return !1;
                        if (!text("关注").findOne(2500)) return !1;
                        if (!text("粉丝").findOne(2500)) return !1;
                        /*
                        // 向服务器请求验证粉丝数和关注数是否符合要求（起水模式屏幕一第二次）
                        let result2 = eval(serverRequest("qs_screenOne_2_2", text("粉丝").findOne().parent().child(2).text())),
                            result3 = eval(serverRequest("qs_screenOne_2_3", text("关注").findOne().parent().child(0).text()));
                        if (result2 || result3) return !1;
                        */
                        // 向服务器请求验证关注数是否符合条件（起水模式屏幕一第二次）
                        //修改20 大于50 就是true
                        //let result4 = eval(serverRequest("qs_screenOne_2_4", text("关注").findOne().parent().child(0).text()));
                        //if (result4) return !1;
                        if (text("关注").findOne().parent().child(0).text() > 50) return !1;
                        if (!storages.create("stupid").get("stupid")) return !1;
                        // 向服务器请求验证粉丝数是否符合特定条件（起水模式屏幕一第二次）

                        //let result5 = eval(serverRequest("qs_screenOne_2_5", text("粉丝").findOne().parent().child(2).text()));
                        //if (result5) return !1;
                        if (text("粉丝").findOne().parent().child(2).text() < 500) return !1;
                        var dyh = text("关注").clickable(!0).findOne().parent().parent().parent().child(0).child(0).text(),
                            bound = text("粉丝").clickable(!0).findOne().bounds(),
                            x = bound.centerX(),
                            y = bound.centerY();
                        if (click(x, y), sleep(5e3), !textContains("抖音粉丝").findOnce()) return !1;
                        // 向服务器请求验证关注按钮数量是否符合要求（起水模式屏幕一第二次）
                        //修改21 只有0才为true
                        //let result6 = eval(serverRequest("qs_screenOne_2_6", text("关注").clickable(!0).find().size()));
                        let result6 = text("关注").clickable(!0).find().size() == 0;
                        return !result6 && (storages.create("usedBenchmarking").put(dyh, 1), textContains("抖音粉丝").findOne().parent().click(), sleep(3e3), textContains("抖音粉丝").findOne().parent().click(), sleep(3e3), !0)
                    }
                    if (storages.create("xihuan").get("xihuan")) {
                        if (!descContains("喜欢").clickable(!0).findOne(500)) return !1;
                        if (descContains("喜欢").clickable(!0).findOne().desc().split(",")[0].split(" ")[1] < 5e3) return !1;
                        let e;
                        if (e = "火山号" === storages.create("whatHao").get("whatHao") ? "火山号" : "抖音号", !textContains(e).findOnce()) return !1;
                        if (1 == storages.create("usedBenchmarking").get(textContains(e).findOne().text().slice(4))) return log("该对标2已经使用过"), !1;
                        if (!text("关注").findOne(2500)) return !1;
                        if (!text("粉丝").findOne(2500)) return !1;
                        if ("万" === text("粉丝").findOne().parent().child(0).text().replace(/[^\u4E00-\u9FA5]/g, "") || "万" === text("关注").findOne().parent().child(0).text().replace(/[^\u4E00-\u9FA5]/g, "")) return !1;
                        if (text("关注").findOne().parent().child(0).text() > 50) return !1;
                        if (!storages.create("stupid").get("stupid")) return !1;
                        if (text("粉丝").findOne().parent().child(0).text() < 500) return !1;
                        var dyh = textContains(e).findOne().text().slice(4);
                        return storages.create("usedBenchmarking").put(dyh, 1), log("进入喜欢列表"), descContains("喜欢").clickable(!0).findOne().click(), sleep(5e3), log("打开第一个作品"), click(100, device.height / 1.5), sleep(5e3), log("翻页"), swipe(device.width / 2, device.height / 2, device.width / 2, 0, 100), sleep(5e3), log("开始"), !0
                    } {
                        let dyhorhsh;
                        if (dyhorhsh = "火山号" === storages.create("whatHao").get("whatHao") ? "火山号" : "抖音号", !textContains(dyhorhsh).findOnce()) return !1;
                        // 向服务器请求验证该对标账号是否已被使用过（正常模式屏幕一第二次）
                        //修改23 依旧仿照
                        //let result1 = eval(serverRequest("screenOne_2_1", storages.create("usedBenchmarking").get(textContains(dyhorhsh).findOne().text().slice(4))));
                        //if (result1) return log("该对标2已经使用过"), !1;
                        if (!(storages.create("usedBenchmarking").get(textContains(dyhorhsh).findOne().text().slice(4)) == undefined)) return !1;
                        if (!text("关注").findOne(2500)) return !1;
                        if (!text("粉丝").findOne(2500)) return !1;
                        // 向服务器请求验证粉丝数和关注数是否符合要求（正常模式屏幕一第二次）
                        /*
                        let result2 = eval(serverRequest("screenOne_2_2", text("粉丝").findOne().parent().child(0).text().replace(/[^\u4E00-\u9FA5]/g, ""))),
                            result3 = eval(serverRequest("screenOne_2_3", text("关注").findOne().parent().child(0).text().replace(/[^\u4E00-\u9FA5]/g, "")));
                        if (result2 || result3) return !1;
                        */
                        // 向服务器请求验证关注数是否符合条件（正常模式屏幕一第二次）
                        //let result4 = eval(serverRequest("screenOne_2_4", text("关注").findOne().parent().child(0).text()));
                        //if (result4) return !1;
                        if (text("关注").findOne().parent().child(0).text() > 50) return !1;
                        if (!storages.create("stupid").get("stupid")) return !1;
                        // 向服务器请求验证粉丝数是否符合特定条件（正常模式屏幕一第二次）
                        //修改24 小于500才是true
                        //let result5 = eval(serverRequest("screenOne_2_5", text("粉丝").findOne().parent().child(0).text()));
                        //if (result5) return !1;
                        if (text("粉丝").findOne().parent().child(0).text() < 500) return !1;
                        var dyh = textContains(dyhorhsh).findOne().text().slice(4);
                        return text("粉丝").findOne().parent().click(), sleep(5e3), !!id(idName + ":id/root_layout").clickable(!0).findOnce() && (storages.create("usedBenchmarking").put(dyh, 1), !0)
                    }
                }

                /**
                 * 检查是否满足操作条件
                 * @param {string} e - 目标用户标识
                 * @param {string} t - 附加参数（未使用）
                 * @returns {boolean} 是否满足操作条件
                 */
                function meetTheOperatingConditions(e, t) {
                    if (storages.create("qishui").get("qishui")) return !(!(text("获赞").findOne(2500) && text("关注").findOne(2500) && text("粉丝").findOne(2500)) || !className("android.widget.FrameLayout").depth(18).clickable(!0).findOne(2500) && !className("android.widget.FrameLayout").depth(17).clickable(!0).findOne(2500) || !text("关注").clickable(!0).findOne(2500) || (1 == storages.create("didTask").get(text("关注").clickable(!0).findOne().parent().parent().parent().child(0).child(0).text()) ? (log("该账号已经操作过"), 1) : text("粉丝").findOne().parent().child(2).text().split("w+").length > 1 || text("关注").findOne().parent().child(0).text().split("w+").length > 1));
                    if (!textContains(e).findOnce()) return !1;
                    if (1 == storages.create("didTask").get(textContains(e).findOne().text())) return log("该账号已经操作过"), !1;
                    if (storages.create("liunan").get("liunan") && storages.create("liunv").get("liunv"));
                    else if (storages.create("liunan").get("liunan") || storages.create("liunv").get("liunv"))
                        if (storages.create("liunan").get("liunan") && !storages.create("liunv").get("liunv")) {
                            if (!descContains("男").findOnce()) return !1
                        } else if (!storages.create("liunan").get("liunan") && storages.create("liunv").get("liunv") && !descContains("女").findOnce()) return !1;
                    return !!text("关注").findOne(2500) && !!text("粉丝").findOne(2500) && "万" !== text("粉丝").findOne().parent().child(0).text().replace(/[^\u4E00-\u9FA5]/g, "") && "万" !== text("关注").findOne().parent().child(0).text().replace(/[^\u4E00-\u9FA5]/g, "")
                }

                /**
                 * 根据任务类型返回对应的序列号
                 * @param {Array} e - 包含任务类型的数组
                 * @returns {number} 任务类型对应的序列号
                 */
                function returnSerialNumber(e) {
                    return "点赞" == e[0] ? 1 : "图文" == e[0] ? 2 : "头像" == e[0] ? 3 : "收藏" == e[0] ? 4 : "评论" == e[0] ? 5 : "关注" == e[0] ? 6 : "私信" == e[0] ? 7 : "分享" == e[0] ? 8 : "点评" == e[0] ? 9 : 10
                }

                /**
                 * 在数组中查找元素的索引，未找到则显示错误并退出
                 * @param {*} e - 要查找的元素
                 * @param {Array} t - 目标数组
                 * @returns {number} 元素在数组中的索引
                 */
                function returnArrIndex(e, t) {
                    for (var r = 0; r < t.length; r++)
                        if (e == t[r]) return r;
                    alert("有序的配置有误", "任务组中没有有序中的任务"), exit()
                }

                /**
                 * 格式化时间为HH:MM:SS格式
                 * @param {Date|number} e - 时间对象或时间戳
                 * @returns {string} 格式化后的时间字符串
                 */
                function restartTime(e) {
                    var t = new Date(e).getHours();
                    t < 10 && (t = "0" + t);
                    var r = new Date(e).getMinutes();
                    r < 10 && (r = "0" + r);
                    var a = new Date(e).getSeconds();
                    return a < 10 && (a = "0" + a), t + ":" + r + ":" + a
                }

                /**
                 * 顺序分组函数，按照元素出现的顺序进行分组处理
                 * @param {Array} e - 输入数组
                 * @returns {Array} 按顺序分组后的数组
                 */
                function shunxufenzu(e) {
                    let t = e,
                        r = {};
                    for (let e of t) r[e] || (r[e] = []), r[e].push(e);
                    let a = [];
                    for (let e of t) a.includes(e) || a.push(e);
                    let s = [],
                        n = !1;
                    for (; !n;) {
                        n = !0;
                        for (let e of a) {
                            let t = r[e];
                            t.length > 0 && (s.push(t.shift()), n = !1)
                        }
                    }
                    return s
                }

                /**
                 * 有序分组函数，保持元素的原始顺序进行分组
                 * @param {Array} e - 输入数组
                 * @returns {Array} 按原始顺序分组后的数组
                 */
                function youxufenzu(e) {
                    let t = new Map,
                        r = [];
                    for (let a of e) t.has(a) || (t.set(a, []), r.push(a)), t.get(a).push(a);
                    let a = [];
                    for (let e of r) a = a.concat(t.get(e));
                    return a
                }

                /**
                 * 遍历列表并执行各种任务操作的主要函数
                 * @param {string} idName - 应用ID名称
                 * @param {string} whatHao - 目标账号
                 * @param {number} restartCount - 重启计数
                 * @param {number} countDown - 倒计时
                 * @param {number} seconds - 基础间隔秒数（未使用）
                 * @param {number} seconds_comment - 评论间隔秒数（未使用）
                 * @param {number} seconds_like - 点赞间隔秒数（未使用）
                 * @param {number} seconds_privateLetter - 私信间隔秒数（未使用）
                 * @param {number} seconds_followWithInterest - 关注间隔秒数（未使用）
                 * @param {number} seconds_collect - 收藏间隔秒数（未使用）
                 * @param {number} seconds_profilePicture - 头像点击间隔秒数（未使用）
                 * @param {number} seconds_imageText - 图文间隔秒数（未使用）
                 * @param {number} backHold - 返回等待时间（未使用）
                 * @param {number} seconds_clickComment - 点评间隔秒数（未使用）
                 * @param {string} carmine - 胭脂红参数
                 * @returns {number} 返回执行状态码
                 */
                 //主功能 任务执行函数
                function traverseList(idName, whatHao, restartCount, countDown, seconds, seconds_comment, seconds_like, seconds_privateLetter, seconds_followWithInterest, seconds_collect, seconds_profilePicture, seconds_imageText, backHold, seconds_clickComment, carmine) {
                    // 检查是否启用汽水模式
                    if (storages.create("qishui").get("qishui")) {
                    log("正在执行汽水模式");
                        // 检查是否需要进入休息模式
                        if (storages.create("rest").get("rest") && (new Date).getTime() >= storages.create("meiyunxingxiuxi").get("meiyunxingxiuxi")) {
                            log("进入休息"), home();
                            // 执行休息倒计时
                            for (var restTime = 60 * storages.create("xiuxi").get("xiuxi"); restTime > 0; restTime--) log(restTime + "秒后开始新一轮"), home(), sleep(1e3);
                            // 更新下次休息时间并返回状态码1
                            return storages.create("meiyunxingxiuxi").put("meiyunxingxiuxi", 60 * storages.create("meiyunxing").get("meiyunxing") * 1e3 + (new Date).getTime()), 1
                        }
                        // 遍历所有可点击的"关注"按钮
                        for (var i = 1; i < text("关注").clickable(!0).find().size(); i++) {
                            // 检查是否存在"抖音粉丝"文本，用于页面定位
                            if (textContains("抖音粉丝").findOnce()) {
                                var bound1 = textContains("抖音粉丝").findOne().bounds(),
                                    bound2 = text("关注").clickable(!0).find().get(i).bounds(),
                                    y1 = bound1.centerY(),
                                    y2 = bound2.centerY();
                                // 如果关注按钮位置在抖音粉丝文本上方，则滑动到下一页
                                y2 < y1 && slideTheNextPage()
                            }
                            // 再次检查是否需要进入休息模式
                            if (storages.create("rest").get("rest") && (new Date).getTime() >= storages.create("meiyunxingxiuxi").get("meiyunxingxiuxi")) {
                                log("进入休息"), home();
                                // 执行休息倒计时
                                for (var restTime = 60 * storages.create("xiuxi").get("xiuxi"); restTime > 0; restTime--) log(restTime + "秒后开始新一轮"), home(), sleep(1e3);
                                // 更新下次休息时间并返回状态码1
                                return storages.create("meiyunxingxiuxi").put("meiyunxingxiuxi", 60 * storages.create("meiyunxing").get("meiyunxing") * 1e3 + (new Date).getTime()), 1
                            }
                            // 点击进入用户主页
                            log("进入主页"), text("关注").clickable(!0).find().get(i).parent().child(0).click(), sleep(3e3);
                            // 初始化任务执行状态和各种任务名称
                            var doTrue = !1,
                                taskName = time() + "arr",
                                taskLike = time() + "recordLikeCount_convention",
                                taskImageText = time() + "recordImageTextCount_convention",
                                taskProfilePicture = time() + "recordProfilePictureCount_convention",
                                taskCollect = time() + "recordCollectCount_convention",
                                taskComment = time() + "recordCommentCount_convention",
                                taskFollowWithInterest = time() + "recordFollowWithInterestCount_convention",
                                taskPrivateLetter = time() + "recordPrivateLetterCount_convention",
                                taskShare = time() + "recordShareCount_convention",
                                taskClickComment = time() + "recordClickCommentCount_convention",
                                taskAllCount = time() + "allCount";
                            // 检查是否满足操作条件
                            if (meetTheOperatingConditions(whatHao, idName)) {
                                // 重置无操作计数器
                                storages.create("countNone").put("countNone", 0);
                                // 获取任务数组
                                var arr = storages.create(taskName).get(taskName);
                                // 如果任务数组为空，返回状态码1
                                if (null == arr) return 1;
                                // 如果任务数组长度为0，返回状态码1
                                if (null != arr && 0 == arr.length) return 1;
                                // 无限循环执行任务
                                for (;;) {
                                    // 随机选择一个任务索引和对应的任务类型
                                    var randomeIndex = random(0, arr.length - 1),
                                        randomNumber = arr[randomeIndex];
                                    // 如果任务类型为1（点赞）且点赞开关已开启
                                    if (1 == randomNumber && storages.create("switchLike").get("switchLike")) {
                                        // 如果没有点赞时间记录，立即执行点赞
                                        if (null == storages.create("recordLikeTime").get("recordLikeTime")) {
                                            if (log("点赞"), like()) {
                                                // 点赞成功后更新任务状态，并向服务器报告任务重启检查（当任务数量达到重启阈值时）
                                                //修改25 将eval(serverRequest("traverseListRe", storages.create(taskAllCount).get(taskAllCount) + "_" + restartCount))修改为storages.create(taskAllCount).get(taskAllCount)%restartCount==0
                                                // 更新任务执行状态、移除已完成任务、更新计数器，检查是否达到重启条件
                                                if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskLike).get(taskLike) ? storages.create(taskLike).put(taskLike, 1) : storages.create(taskLike).put(taskLike, storages.create(taskLike).get(taskLike) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                            }
                                            break
                                        }
                                        // 如果已到达预定的点赞时间，执行点赞
                                        if ((new Date).getTime() >= storages.create("recordLikeTime").get("recordLikeTime")) {
                                            if (log("点赞"), like()) {
                                                // 点赞成功后更新任务状态，并向服务器报告任务重启检查（当任务数量达到重启阈值时）
                                                // 更新任务执行状态、移除已完成任务、更新计数器，检查是否达到重启条件
                                                if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskLike).get(taskLike) ? storages.create(taskLike).put(taskLike, 1) : storages.create(taskLike).put(taskLike, storages.create(taskLike).get(taskLike) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                            }
                                            break
                                        }
                                        // 如果是第一次记录时间，显示倒计时信息
                                        0 == timeRecord1 && (log(restartTime(storages.create("recordLikeTime").get("recordLikeTime")) + " 后执行点赞"), timeRecord1 = 1)
                                    }
                                }
                            } else if (null == storages.create("countNone").get("countNone") ? storages.create("countNone").put("countNone", 1) : storages.create("countNone").put("countNone", storages.create("countNone").get("countNone") + 1), storages.create("jieliu").get("jieliu")) {
                                // 如果开启节流模式且无操作次数达到30次，更换对标
                                if (storages.create("countNone").get("countNone") >= 30) return log("对标不好 更换对标"), 1
                            } else if (storages.create("countNone").get("countNone") >= 8) return log("对标不好 更换对标"), 1;
                            // 返回操作循环，确保页面有足够的关注按钮
                            for (var backRecordCount = 0;;) {
                                var size = text("关注").clickable(!0).find().size();
                                // 如果关注按钮数量大于3个，跳出循环
                                if (size > 3) {

                                    break
                                }
                                // 如果已执行任务且找到关注按钮，记录任务并返回上一页
                                if (doTrue && text("关注").clickable(!0).findOne(500) && (storages.create("didTask").put(text("关注").clickable(!0).findOne().parent().parent().parent().child(0).child(0).text(), 1), log(text("关注").clickable(!0).findOne().parent().parent().parent().child(0).child(0).text())), log("返回"), back(), sleep(5e3), backRecordCount++, backRecordCount >= 10) return log("返回异常 重启"), 1
                            }
                        }
                    } else if (storages.create("xihuan").get("xihuan")) {
                    // 执行喜欢模式
                    log("xihuan")
                        // 检查是否需要进入休息模式
                        if (storages.create("rest").get("rest") && (new Date).getTime() >= storages.create("meiyunxingxiuxi").get("meiyunxingxiuxi")) {
                            log("进入休息"), home();
                            // 执行休息倒计时
                            for (var restTime = 60 * storages.create("xiuxi").get("xiuxi"); restTime > 0; restTime--) log(restTime + "秒后开始新一轮"), home(), sleep(1e3);
                            // 更新下次休息时间并返回状态码1
                            return storages.create("meiyunxingxiuxi").put("meiyunxingxiuxi", 60 * storages.create("meiyunxing").get("meiyunxing") * 1e3 + (new Date).getTime()), 1
                        }
                        // 确定是否执行任务的随机判断
                        if (storages.create("is").get("is")) var isTrue = !0;
                        else {
                            var is = random(1, 2);
                            if (1 == is) var isTrue = !0;
                            else var isTrue = !1
                        }
                        // 初始化任务执行状态
                        var doTrue = !1;
                        // 如果随机判断为真，执行任务
                        if (isTrue) {
                            // 初始化各种任务名称和获取任务数组
                            var taskName = time() + "arr",
                                taskLike = time() + "recordLikeCount_convention",
                                taskImageText = time() + "recordImageTextCount_convention",
                                taskProfilePicture = time() + "recordProfilePictureCount_convention",
                                taskCollect = time() + "recordCollectCount_convention",
                                taskComment = time() + "recordCommentCount_convention",
                                taskFollowWithInterest = time() + "recordFollowWithInterestCount_convention",
                                taskPrivateLetter = time() + "recordPrivateLetterCount_convention",
                                taskShare = time() + "recordShareCount_convention",
                                taskClickComment = time() + "recordClickCommentCount_convention",
                                arr = storages.create(taskName).get(taskName);
                            // 如果任务数组为空，返回状态码1
                            if (null == arr) return 1;
                            // 如果任务数组长度为0，返回状态码1
                            if (null != arr && 0 == arr.length) return 1;
                            // 初始化各种时间记录变量，执行任务循环
                            for (var timeRecord1 = 0, timeRecord2 = 0, timeRecord3 = 0, timeRecord4 = 0, timeRecord5 = 0, timeRecord6 = 0, timeRecord7 = 0, timeRecord8 = 0, timeRecord9 = 0;;) {
                                // 固定选择第一个任务（与汽水模式的随机选择不同）
                                var randomeIndex = 0,
                                    randomNumber = arr[randomeIndex];
                                // 如果任务类型为1（点赞）且点赞开关已开启
                                if (1 == randomNumber && storages.create("switchLike").get("switchLike")) {
                                    // 如果没有点赞时间记录，立即执行点赞
                                    if (null == storages.create("recordLikeTime").get("recordLikeTime")) {
                                        if (log("点赞"), like()) {
                                            // 点赞成功后更新任务状态，并向服务器报告任务重启检查
                                            // 更新任务执行状态、移除已完成任务、更新计数器，检查是否达到重启条件
                                            if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskLike).get(taskLike) ? storages.create(taskLike).put(taskLike, 1) : storages.create(taskLike).put(taskLike, storages.create(taskLike).get(taskLike) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                        }
                                        break
                                    }
                                    // 如果已到达预定的点赞时间，执行点赞
                                    if ((new Date).getTime() >= storages.create("recordLikeTime").get("recordLikeTime")) {
                                        if (log("点赞"), like()) {
                                            // 点赞成功后更新任务状态，并向服务器报告任务重启检查
                                            // 更新任务执行状态、移除已完成任务、更新计数器，检查是否达到重启条件
                                            if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskLike).get(taskLike) ? storages.create(taskLike).put(taskLike, 1) : storages.create(taskLike).put(taskLike, storages.create(taskLike).get(taskLike) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                        }
                                        break
                                    }
                                    // 如果是第一次记录时间，显示倒计时信息
                                    0 == timeRecord1 && (log(restartTime(storages.create("recordLikeTime").get("recordLikeTime")) + " 后执行点赞"), timeRecord1 = 1)
                                // 如果任务类型为4（收藏）且收藏开关已开启
                                } else if (4 == randomNumber && storages.create("switchCollect").get("switchCollect")) {
                                    // 如果没有收藏时间记录，立即执行收藏
                                    if (null == storages.create("recordCollectTime").get("recordCollectTime")) {
                                        if (log("收藏"), collect()) {
                                            // 收藏成功后更新任务状态，并向服务器报告任务重启检查
                                            // 更新任务执行状态、移除已完成任务、更新计数器，检查是否达到重启条件
                                            if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskCollect).get(taskCollect) ? storages.create(taskCollect).put(taskCollect, 1) : storages.create(taskCollect).put(taskCollect, storages.create(taskCollect).get(taskCollect) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                        }
                                        break
                                    }
                                    // 如果已到达预定的收藏时间，执行收藏
                                    if ((new Date).getTime() >= storages.create("recordCollectTime").get("recordCollectTime")) {
                                        if (log("收藏"), collect()) {
                                            // 收藏成功后更新任务状态，并向服务器报告任务重启检查
                                            // 更新任务执行状态、移除已完成任务、更新计数器，检查是否达到重启条件
                                            if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskCollect).get(taskCollect) ? storages.create(taskCollect).put(taskCollect, 1) : storages.create(taskCollect).put(taskCollect, storages.create(taskCollect).get(taskCollect) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                        }
                                        break
                                    }
                                    // 如果是第一次记录时间，显示倒计时信息
                                    0 == timeRecord4 && (log(restartTime(storages.create("recordCollectTime").get("recordCollectTime")) + " 后执行收藏"), timeRecord4 = 1)
                                } else {
                                    // 如果任务类型为5（评论）
                                    if (5 == randomNumber)
                                        // 如果启用白屏模式
                                        if (storages.create("baiping").get("baiping")) {
                                            // 检查时间是否在8点之后且评论开关已开启
                                            if ((new Date).getHours() >= 8 && storages.create("switchComment").get("switchComment")) {
                                                // 如果没有评论时间记录，立即执行评论
                                                if (null == storages.create("recordCommentTime").get("recordCommentTime")) {
                                                    if (log("评论"), comment(idName)) {
                                                        // 评论成功后更新任务状态，并向服务器报告任务重启检查
                                                        // 更新任务执行状态、移除已完成任务、更新计数器，检查是否达到重启条件
                                                        if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskComment).get(taskComment) ? storages.create(taskComment).put(taskComment, 1) : storages.create(taskComment).put(taskComment, storages.create(taskComment).get(taskComment) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                    }
                                                    break
                                                }
                                                // 如果已到达预定的评论时间，执行评论
                                                if ((new Date).getTime() >= storages.create("recordCommentTime").get("recordCommentTime")) {
                                                    if (log("评论"), comment(idName)) {
                                                        // 评论成功后更新任务状态，并向服务器报告任务重启检查
                                                        // 更新任务执行状态、移除已完成任务、更新计数器，检查是否达到重启条件
                                                        if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskComment).get(taskComment) ? storages.create(taskComment).put(taskComment, 1) : storages.create(taskComment).put(taskComment, storages.create(taskComment).get(taskComment) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                    }
                                                    break
                                                }
                                                // 如果是第一次记录时间，显示倒计时信息
                                                0 == timeRecord5 && (log(restartTime(storages.create("recordCommentTime").get("recordCommentTime")) + " 后执行评论"), timeRecord5 = 1);
                                                continue
                                            }
                                        // 如果未启用白屏模式但评论开关已开启
                                        } else if (storages.create("switchComment").get("switchComment")) {
                                        // 如果没有评论时间记录，立即执行评论
                                        if (null == storages.create("recordCommentTime").get("recordCommentTime")) {
                                            if (log("评论"), comment(idName)) {
                                                // 评论成功后更新任务状态，并向服务器报告任务重启检查
                                                // 更新任务执行状态、移除已完成任务、更新计数器，检查是否达到重启条件
                                                if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskComment).get(taskComment) ? storages.create(taskComment).put(taskComment, 1) : storages.create(taskComment).put(taskComment, storages.create(taskComment).get(taskComment) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                            }
                                            break
                                        }
                                        // 如果已到达预定的评论时间，执行评论
                                        if ((new Date).getTime() >= storages.create("recordCommentTime").get("recordCommentTime")) {
                                            if (log("评论"), comment(idName)) {
                                                // 评论成功后更新任务状态，并向服务器报告任务重启检查
                                                // 更新任务执行状态、移除已完成任务、更新计数器，检查是否达到重启条件
                                                if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskComment).get(taskComment) ? storages.create(taskComment).put(taskComment, 1) : storages.create(taskComment).put(taskComment, storages.create(taskComment).get(taskComment) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                            }
                                            break
                                        }
                                        // 如果是第一次记录时间，显示倒计时信息
                                        0 == timeRecord5 && (log(restartTime(storages.create("recordCommentTime").get("recordCommentTime")) + " 后执行评论"), timeRecord5 = 1);
                                        continue
                                    }
                                    // 如果任务类型为8（分享）且分享开关已开启
                                    if (8 == randomNumber && storages.create("switchShare").get("switchShare")) {
                                        // 如果没有分享时间记录，立即执行分享
                                        if (null == storages.create("recordShareTime").get("recordShareTime")) {
                                            if (log("分享"), share()) {
                                                // 分享成功后更新任务状态，并向服务器报告任务重启检查
                                                // 更新任务执行状态、移除已完成任务、更新计数器，检查是否达到重启条件
                                                if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskShare).get(taskShare) ? storages.create(taskShare).put(taskShare, 1) : storages.create(taskShare).put(taskShare, storages.create(taskShare).get(taskShare) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                            }
                                            break
                                        }
                                        // 如果已到达预定的分享时间，执行分享
                                        if ((new Date).getTime() >= storages.create("recordShareTime").get("recordShareTime")) {
                                            if (log("分享"), share()) {
                                                // 分享成功后更新任务状态，并向服务器报告任务重启检查
                                                // 更新任务执行状态、移除已完成任务、更新计数器，检查是否达到重启条件
                                                if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskShare).get(taskShare) ? storages.create(taskShare).put(taskShare, 1) : storages.create(taskShare).put(taskShare, storages.create(taskShare).get(taskShare) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                            }
                                            break
                                        }
                                        // 如果是第一次记录时间，显示倒计时信息
                                        0 == timeRecord8 && (log(restartTime(storages.create("recordShareTime").get("recordShareTime")) + " 后执行分享"), timeRecord8 = 1)
                                    // 如果任务类型为9（点评）且点评开关已开启
                                    } else if (9 == randomNumber && storages.create("switchClickComment").get("switchClickComment")) {
                                        // 如果没有点评时间记录，立即执行点评
                                        if (null == storages.create("recordClickCommentTime").get("recordClickCommentTime")) {
                                            if (log("点评"), clickComment()) {
                                                // 点评成功后更新任务状态，并向服务器报告任务重启检查
                                                // 更新任务执行状态、移除已完成任务、更新计数器，检查是否达到重启条件
                                                if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskClickComment).get(taskClickComment) ? storages.create(taskClickComment).put(taskClickComment, 1) : storages.create(taskClickComment).put(taskClickComment, storages.create(taskClickComment).get(taskClickComment) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                            }
                                            break
                                        }
                                        // 如果已到达预定的点评时间，执行点评
                                        if ((new Date).getTime() >= storages.create("recordClickCommentTime").get("recordClickCommentTime")) {
                                            if (log("点评"), clickComment()) {
                                                // 点评成功后更新任务状态，并向服务器报告任务重启检查
                                                // 更新任务执行状态、移除已完成任务、更新计数器，检查是否达到重启条件
                                                if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskClickComment).get(taskClickComment) ? storages.create(taskClickComment).put(taskClickComment, 1) : storages.create(taskClickComment).put(taskClickComment, storages.create(taskClickComment).get(taskClickComment) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                            }
                                            break
                                        }
                                        // 如果是第一次记录时间，显示倒计时信息
                                        0 == timeRecord9 && (log(restartTime(storages.create("recordClickCommentTime").get("recordClickCommentTime")) + " 后执行点评"), timeRecord9 = 1)
                                    }
                                }
                            }
                        }
                    } else
                    // 正版模式处理分支
                    log("正版")
                        // 遍历所有根布局元素
                        for (var i = 0; i < id(idName + ":id/root_layout").find().size(); i++) {
                            // 检查时间控制：如果设置了任何时间点控制且当前时间不在允许的时间段内，则返回状态码1
                            // 这里检查0-23点的所有时间控制开关，如果启用了时间控制但当前时间不匹配，则停止执行
                            if ((storages.create("lingdian").get("lingdian") || storages.create("yidian").get("yidian") || storages.create("erdian").get("erdian") || storages.create("sandian").get("sandian") || storages.create("sidian").get("sidian") || storages.create("wudian").get("wudian") || storages.create("liudian").get("liudian") || storages.create("qidian").get("qidian") || storages.create("badian").get("badian") || storages.create("jiudian").get("jiudian") || storages.create("shidian").get("shidian") || storages.create("shiyidian").get("shiyidian") || storages.create("shierdian").get("shierdian") || storages.create("shisandian").get("shisandian") || storages.create("shisidian").get("shisidian") || storages.create("shiwudian").get("shiwudian") || storages.create("shiliudian").get("shiliudian") || storages.create("shiqidian").get("shiqidian") || storages.create("shibadian").get("shibadian") || storages.create("shijiudian").get("shijiudian") || storages.create("ershidian").get("ershidian") || storages.create("ershiyidian").get("ershiyidian") || storages.create("ershierdian").get("ershierdian") || storages.create("ershisandian").get("ershisandian")) && !(0 == (new Date).getHours() && storages.create("lingdian").get("lingdian") || 1 == (new Date).getHours() && storages.create("yidian").get("yidian") || 2 == (new Date).getHours() && storages.create("erdian").get("erdian") || 3 == (new Date).getHours() && storages.create("sandian").get("sandian") || 4 == (new Date).getHours() && storages.create("sidian").get("sidian") || 5 == (new Date).getHours() && storages.create("wudian").get("wudian") || 6 == (new Date).getHours() && storages.create("liudian").get("liudian") || 7 == (new Date).getHours() && storages.create("qidian").get("qidian") || 8 == (new Date).getHours() && storages.create("badian").get("badian") || 9 == (new Date).getHours() && storages.create("jiudian").get("jiudian") || 10 == (new Date).getHours() && storages.create("shidian").get("shidian") || 11 == (new Date).getHours() && storages.create("shiyidian").get("shiyidian") || 12 == (new Date).getHours() && storages.create("shierdian").get("shierdian") || 13 == (new Date).getHours() && storages.create("shisandian").get("shisandian") || 14 == (new Date).getHours() && storages.create("shisidian").get("shisidian") || 15 == (new Date).getHours() && storages.create("shiwudian").get("shiwudian") || 16 == (new Date).getHours() && storages.create("shiliudian").get("shiliudian") || 17 == (new Date).getHours() && storages.create("shiqidian").get("shiqidian") || 18 == (new Date).getHours() && storages.create("shibadian").get("shibadian") || 19 == (new Date).getHours() && storages.create("shijiudian").get("shijiudian") || 20 == (new Date).getHours() && storages.create("ershidian").get("ershidian") || 21 == (new Date).getHours() && storages.create("ershiyidian").get("ershiyidian") || 22 == (new Date).getHours() && storages.create("ershierdian").get("ershierdian") || 23 == (new Date).getHours() && storages.create("ershisandian").get("ershisandian"))) return 1;
                            // 检查是否需要进入休息模式
                            if (storages.create("rest").get("rest") && (new Date).getTime() >= storages.create("meiyunxingxiuxi").get("meiyunxingxiuxi")) {
                                log("进入休息"), home();
                                // 执行休息倒计时
                                for (var restTime = 60 * storages.create("xiuxi").get("xiuxi"); restTime > 0; restTime--) log(restTime + "秒后开始新一轮"), home(), sleep(1e3);
                                // 更新下次休息时间并返回状态码1
                                return storages.create("meiyunxingxiuxi").put("meiyunxingxiuxi", 60 * storages.create("meiyunxing").get("meiyunxing") * 1e3 + (new Date).getTime()), 1
                            }
                            // 检查是否出现应用关闭提示
                            if (textContains("关闭应用").findOnce() && textContains("等待").findOnce()) {
                                var bound = textContains("等待").findOne().bounds(),
                                    x = bound.centerX(),
                                    y = bound.centerY();
                                return click(x, y), sleep(5e3), 1
                            }
                            // 检查UI元素结构：确保不是ImageView、不是"回关"文本、且子元素数量为1
                            if ("android.widget.ImageView" != id(idName + ":id/root_layout").find().get(i).child(2).child(0).className() && "回关" !== id(idName + ":id/root_layout").find().get(i).child(2).child(0).child(0).text() && 1 == id(idName + ":id/root_layout").find().get(i).child(1).child(0).childCount())
                                // 检查是否已经访问过该主页（通过主页名称记录）
                                if (1 != storages.create("homepageName").get(id(idName + ":id/root_layout").find().get(i).desc())) {
                                    // 再次检查应用关闭提示
                                    if (textContains("关闭应用").findOnce() && textContains("等待").findOnce()) {
                                        var bound = textContains("等待").findOne().bounds(),
                                            x = bound.centerX(),
                                            y = bound.centerY();
                                        return click(x, y), sleep(5e3), 1
                                    }
                                    // 获取当前UI控件并点击进入用户主页
                                    var kongjian = id(idName + ":id/root_layout").find().get(i);
                                    // 如果未启用留痕模式，执行任务操作
                                    if (log(`进入 ${kongjian.desc().split(",")[0]}`), kongjian.click(), sleep(3e3), !storages.create("liuhen").get("liuhen"))
                                        // 如果启用新婚模式
                                        if (storages.create("xinhun").get("xinhun"))
                                            // 检查新婚模式的时间限制
                                            if (null == storages.create("recordxinhun").get("recordxinhun") || (new Date).getTime() >= storages.create("recordxinhun").get("recordxinhun")) {
                                                // 初始化任务执行状态
                                                var doTrue = !1;
                                                // 检查应用关闭提示
                                                if (textContains("关闭应用").findOnce() && textContains("等待").findOnce()) {
                                                    var bound = textContains("等待").findOne().bounds(),
                                                        x = bound.centerX(),
                                                        y = bound.centerY();
                                                    return click(x, y), sleep(5e3), 1
                                                }
                                                // 初始化各种任务名称
                                                var taskName = time() + "arr",
                                                    taskLike = time() + "recordLikeCount_convention",
                                                    taskImageText = time() + "recordImageTextCount_convention",
                                                    taskProfilePicture = time() + "recordProfilePictureCount_convention",
                                                    taskCollect = time() + "recordCollectCount_convention",
                                                    taskComment = time() + "recordCommentCount_convention",
                                                    taskFollowWithInterest = time() + "recordFollowWithInterestCount_convention",
                                                    taskPrivateLetter = time() + "recordPrivateLetterCount_convention",
                                                    taskShare = time() + "recordShareCount_convention",
                                                    taskClickComment = time() + "recordClickCommentCount_convention",
                                                    taskAllCount = time() + "allCount";
                                                // 检查用户是否有作品且不是私密账号
                                                if (!text("暂无作品").findOnce() && !text("私密账号").findOnce())
                                                    // 等待点赞数元素加载，重置检测任务状态，检查操作条件
                                                    if (descContains("点赞数").findOne(8e3), storages.create("jiance").get("jiance") && storages.create("taskOfImageText").put("taskOfImageText", 0), storages.create("jiance").get("jiance") && storages.create("taskOfLike").put("taskOfLike", 0), meetTheOperatingConditions(whatHao, idName)) {
                                                        if (textContains("关闭应用").findOnce() && textContains("等待").findOnce()) {
                                                            var bound = textContains("等待").findOne().bounds(),
                                                                x = bound.centerX(),
                                                                y = bound.centerY();
                                                            return click(x, y), sleep(5e3), 1
                                                        }
                                                        // 重置无操作计数器，记录主页名称
                                                        storages.create("countNone").put("countNone", 0), descContains("复制名字").findOnce() && storages.create("homepageName").put(descContains("复制名字").findOne().text(), 1);
                                                        // 获取任务数组
                                                        var arr = storages.create(taskName).get(taskName);
                                                        // 如果任务数组为空，返回状态码1
                                                        if (null == arr) return 1;
                                                        // 如果任务数组长度为0，返回状态码1
                                                        if (null != arr && 0 == arr.length) return 1;
                                                        // 初始化各种时间记录变量，执行任务循环
                                                        for (var timeRecord1 = 0, timeRecord2 = 0, timeRecord3 = 0, timeRecord4 = 0, timeRecord5 = 0, timeRecord6 = 0, timeRecord7 = 0, timeRecord8 = 0, timeRecord9 = 0;;) {
                                                            // 检查应用关闭提示
                                                            if (textContains("关闭应用").findOnce() && textContains("等待").findOnce()) {
                                                                var bound = textContains("等待").findOne().bounds(),
                                                                    x = bound.centerX(),
                                                                    y = bound.centerY();
                                                                return click(x, y), sleep(5e3), 1
                                                            }
                                                            // 如果启用有序模式
                                                            if (storages.create("youxu").get("youxu")) {
                                                                // 获取有序数组
                                                                var youxuArr = storages.create("youxuArr").get("youxuArr");
                                                                // 如果有序数组为空，重新初始化
                                                                if (null == youxuArr || 0 == youxuArr.length) {
                                                                    var yxArr = [storages.create("youxu1").get("youxu1"), storages.create("youxu2").get("youxu2"), storages.create("youxu3").get("youxu3"), storages.create("youxu4").get("youxu4"), storages.create("youxu5").get("youxu5"), storages.create("youxu6").get("youxu6"), storages.create("youxu7").get("youxu7"), storages.create("youxu8").get("youxu8"), storages.create("youxu9").get("youxu9"), storages.create("youxu10").get("youxu10"), storages.create("youxu11").get("youxu11"), storages.create("youxu12").get("youxu12"), storages.create("youxu13").get("youxu13"), storages.create("youxu14").get("youxu14"), storages.create("youxu15").get("youxu15")];
                                                                    storages.create("youxuArr").put("youxuArr", yxArr)
                                                                }
                                                                // 获取有序数组和序列号
                                                                var youxuArr = storages.create("youxuArr").get("youxuArr"),
                                                                    serialNumber = returnSerialNumber(youxuArr);
                                                                // 如果序列号为10（错误状态），显示错误并退出
                                                                10 == serialNumber && (alert("有序的配置有误", "检查输入是否正确"), exit());
                                                                // 根据序列号获取任务索引和任务类型
                                                                var randomeIndex = returnArrIndex(serialNumber, arr),
                                                                    randomNumber = arr[randomeIndex]
                                                            // 如果启用检测模式，根据不同的策略选择任务
                                                            } else if (storages.create("jiance").get("jiance"))
                                                                // 如果启用赞颜模式，随机选择任务
                                                                if (storages.create("zanyan").get("zanyan")) var randomeIndex = random(0, arr.length - 1),
                                                                    randomNumber = arr[randomeIndex];
                                                                // 如果启用新有序模式，选择第一个任务
                                                                else if (storages.create("xinyouxu").get("xinyouxu")) var randomeIndex = 0,
                                                                randomNumber = arr[randomeIndex];
                                                            // 如果启用活力模式，选择第一个任务
                                                            else if (storages.create("huoli").get("huoli")) var randomeIndex = 0,
                                                                randomNumber = arr[randomeIndex];
                                                            // 如果启用顺序模式，选择第一个任务
                                                            else if (storages.create("shunxu").get("shunxu")) var randomeIndex = 0,
                                                                randomNumber = arr[randomeIndex];
                                                            // 如果启用图文开关且图文数量大于0
                                                            else if (storages.create("switchImageText").get("switchImageText") && storages.create("imageText").get("imageText") > 0)
                                                                // 如果图文时间已到或未设置，寻找图文任务（类型2）
                                                                if (null == storages.create("imageTextNextTimes").get("imageTextNextTimes") || (new Date).getTime() >= storages.create("imageTextNextTimes").get("imageTextNextTimes"))
                                                                    for (;;) {
                                                                        var randomeIndex = random(0, arr.length - 1),
                                                                            randomNumber = arr[randomeIndex];
                                                                        // 找到图文任务就跳出循环
                                                                        if (2 == randomNumber) break;
                                                                        // 如果任务数组为空则返回
                                                                        if (null == storages.create(time() + "arr").get(time() + "arr")) return 1
                                                                    // 如果图文时间未到但点赞开关开启且点赞数量大于0
                                                                    } else if (storages.create("switchLike").get("switchLike") && storages.create("like").get("like") > 0)
                                                                        // 如果点赞时间已到或未设置，寻找点赞任务（类型1）
                                                                        if (null == storages.create("recordLikeTime").get("recordLikeTime") || (new Date).getTime() >= storages.create("recordLikeTime").get("recordLikeTime"))
                                                                            for (;;) {
                                                                                var randomeIndex = random(0, arr.length - 1),
                                                                                    randomNumber = arr[randomeIndex];
                                                                                // 找到点赞任务就跳出循环
                                                                                if (1 == randomNumber) break;
                                                                                // 如果任务数组为空则返回
                                                                                if (null == storages.create(time() + "arr").get(time() + "arr")) return 1
                                                                            // 如果点赞时间未到，显示等待时间并寻找其他任务
                                                                            } else
                                                                                for (log(restartTime(storages.create("imageTextNextTimes").get("imageTextNextTimes")) + " 后执行图文"), log(restartTime(storages.create("recordLikeTime").get("recordLikeTime")) + " 后执行点赞");;) {
                                                                                    var randomeIndex = random(0, arr.length - 1),
                                                                                        randomNumber = arr[randomeIndex];
                                                                                    // 跳过点赞和图文任务，寻找其他任务
                                                                                    if (1 != randomNumber && 2 != randomNumber) break;
                                                                                    // 如果图文时间已到，优先执行图文任务
                                                                                    if (null == storages.create("imageTextNextTimes").get("imageTextNextTimes") || (new Date).getTime() >= storages.create("imageTextNextTimes").get("imageTextNextTimes")) {
                                                                                        for (;;) {
                                                                                            var randomeIndex = random(0, arr.length - 1),
                                                                                                randomNumber = arr[randomeIndex];
                                                                                            // 找到图文任务就跳出循环
                                                                                            if (2 == randomNumber) break;
                                                                                            // 如果任务数组为空则返回
                                                                                            if (null == storages.create(time() + "arr").get(time() + "arr")) return 1
                                                                                        }
                                                                                        break
                                                                                    }
                                                                                    // 如果点赞时间已到，优先执行点赞任务
                                                                                    if (null == storages.create("recordLikeTime").get("recordLikeTime") || (new Date).getTime() >= storages.create("recordLikeTime").get("recordLikeTime")) {
                                                                                        for (;;) {
                                                                                            var randomeIndex = random(0, arr.length - 1),
                                                                                                randomNumber = arr[randomeIndex];
                                                                                            // 找到点赞任务就跳出循环
                                                                                            if (1 == randomNumber) break;
                                                                                            // 如果任务数组为空则返回
                                                                                            if (null == storages.create(time() + "arr").get(time() + "arr")) return 1
                                                                                        }
                                                                                        break
                                                                                    }
                                                                                // 如果都不满足条件，随机选择任务
                                                                                } else var randomeIndex = random(0, arr.length - 1),
                                                                                    randomNumber = arr[randomeIndex];
                                                                        // 如果图文未开启但点赞开关开启且点赞数量大于0
                                                                        else if (storages.create("switchLike").get("switchLike") && storages.create("like").get("like") > 0)
                                                                // 如果点赞时间已到或未设置，寻找点赞任务
                                                                if (null == storages.create("recordLikeTime").get("recordLikeTime") || (new Date).getTime() >= storages.create("recordLikeTime").get("recordLikeTime"))
                                                                    for (;;) {
                                                                        var randomeIndex = random(0, arr.length - 1),
                                                                            randomNumber = arr[randomeIndex];
                                                                        // 找到点赞任务就跳出循环
                                                                        if (1 == randomNumber) break;
                                                                        // 如果任务数组为空则返回
                                                                        if (null == storages.create(time() + "arr").get(time() + "arr")) return 1
                                                                    // 如果点赞时间未到，显示等待时间并寻找其他任务
                                                                    } else
                                                                        for (log(restartTime(storages.create("recordLikeTime").get("recordLikeTime")) + " 后执行点赞");;) {
                                                                            var randomeIndex = random(0, arr.length - 1),
                                                                                randomNumber = arr[randomeIndex];
                                                                            // 跳过点赞任务，寻找其他任务
                                                                            if (1 != randomNumber) break;
                                                                            // 如果点赞时间已到，执行点赞任务
                                                                            if (null == storages.create("recordLikeTime").get("recordLikeTime") || (new Date).getTime() >= storages.create("recordLikeTime").get("recordLikeTime")) {
                                                                                for (;;) {
                                                                                    var randomeIndex = random(0, arr.length - 1),
                                                                                        randomNumber = arr[randomeIndex];
                                                                                    // 找到点赞任务就跳出循环
                                                                                    if (1 == randomNumber) break;
                                                                                    // 如果任务数组为空则返回
                                                                                    if (null == storages.create(time() + "arr").get(time() + "arr")) return 1
                                                                                }
                                                                                break
                                                                            }
                                                                        // 如果都不满足条件，随机选择任务
                                                                        } else var randomeIndex = random(0, arr.length - 1),
                                                                            randomNumber = arr[randomeIndex];
                                                                // 如果活力模式开启，选择第一个任务
                                                                else if (storages.create("huoli").get("huoli")) var randomeIndex = 0,
                                                                randomNumber = arr[randomeIndex];
                                                            // 如果新有序模式开启，选择第一个任务
                                                            else if (storages.create("xinyouxu").get("xinyouxu")) var randomeIndex = 0,
                                                                randomNumber = arr[randomeIndex];
                                                            // 如果赞颜模式开启，随机选择任务
                                                            else if (storages.create("zanyan").get("zanyan")) var randomeIndex = random(0, arr.length - 1),
                                                                randomNumber = arr[randomeIndex];
                                                            // 如果顺序模式开启，选择第一个任务
                                                            else if (storages.create("shunxu").get("shunxu")) var randomeIndex = 0,
                                                                randomNumber = arr[randomeIndex];
                                                            // 默认情况下随机选择任务
                                                            else var randomeIndex = random(0, arr.length - 1),
                                                                randomNumber = arr[randomeIndex];
                                                            // 如果任务类型为1（点赞）且点赞开关已开启
                                                            if (1 == randomNumber && storages.create("switchLike").get("switchLike")) {
                                                                // 如果没有点赞时间记录，立即执行点赞
                                                                if (null == storages.create("recordLikeTime").get("recordLikeTime")) {
                                                                    if (log("点赞"), like()) {
                                                                        // 点赞成功后更新任务状态，并向服务器报告任务重启检查（当任务数量达到重启阈值时）
                                                                        // 更新任务执行状态、移除已完成任务、短暂休眠、更新任务数组、更新总计数器和点赞计数器、检查重启条件
                                                                        // ==================== 点赞任务成功后的状态更新处理 ====================
                                                                        if (
                                                                            // 步骤1: 标记任务执行成功状态
                                                                            doTrue = true, // 将 !0 改为更清晰的 true

                                                                            // 步骤2: 处理有序模式的任务队列管理
                                                                            // 如果启用了有序模式，需要从有序任务数组中移除已完成的任务
                                                                            storages.create("youxu").get("youxu") && (
                                                                                youxuArr.splice(0, 1), // 移除有序数组的第一个元素（已完成的任务）
                                                                                storages.create("youxuArr").put("youxuArr", youxuArr) // 保存更新后的有序任务数组
                                                                            ),

                                                                            // 步骤3: 从当前任务数组中移除已完成的任务
                                                                            arr.splice(randomeIndex, 1), // 根据随机索引移除已执行的任务

                                                                            // 步骤4: 执行短暂休眠，避免操作过于频繁
                                                                            sleep(100), // 休眠100毫秒，防止操作过快导致系统不稳定

                                                                            // 步骤5: 将更新后的任务数组保存到持久化存储
                                                                            storages.create(taskName).put(taskName, arr),

                                                                            // 步骤6: 更新总任务执行计数器
                                                                            // 检查总计数器是否存在，不存在则初始化为1，存在则递增
                                                                            null == storages.create(taskAllCount).get(taskAllCount) ?
                                                                                storages.create(taskAllCount).put(taskAllCount, 1) : // 首次执行任务，初始化计数器
                                                                                storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), // 累加总任务计数

                                                                            // 步骤7: 更新点赞任务专用计数器
                                                                            // 检查点赞计数器是否存在，不存在则初始化为1，存在则递增
                                                                            null == storages.create(taskLike).get(taskLike) ?
                                                                                storages.create(taskLike).put(taskLike, 1) : // 首次执行点赞任务，初始化计数器
                                                                                storages.create(taskLike).put(taskLike, storages.create(taskLike).get(taskLike) + 1), // 累加点赞任务计数

                                                                            // 步骤8: 检查应用重启条件
                                                                            // 当总任务数达到重启阈值的倍数时，触发应用重启以保持系统稳定性
                                                                            // 使用双重检查确保重启条件的准确性（防止意外重启）
                                                                            storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 &&
                                                                            storages.create(taskAllCount).get(taskAllCount) % restartCount == 0
                                                                        ) {
                                                                            // 达到重启条件时的处理
                                                                            const currentTaskCount = storages.create(taskAllCount).get(taskAllCount);
                                                                            const currentLikeCount = storages.create(taskLike).get(taskLike);

                                                                            // 输出详细的任务执行统计信息
                                                                            log(`🎉 点赞任务执行成功！`);
                                                                            log(`📊 任务统计 - 总任务数: ${currentTaskCount}, 点赞任务数: ${currentLikeCount}`);
                                                                            log(`🔄 达到重启阈值(${restartCount})，准备重启应用以保持系统稳定性`);

                                                                            return 1; // 返回状态码1，触发应用重启流程
                                                                        }

                                                                    }
                                                                    break
                                                                }
                                                                // 如果已到达预定的点赞时间，执行点赞
                                                                if ((new Date).getTime() >= storages.create("recordLikeTime").get("recordLikeTime")) {
                                                                    if (log("点赞2"), like()) {
                                                                        // 点赞成功后更新任务状态，并向服务器报告任务重启检查（当任务数量达到重启阈值时）
                                                                        // 更新任务执行状态、移除已完成任务、短暂休眠、更新任务数组、更新总计数器和点赞计数器、检查重启条件
                                                                        if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskLike).get(taskLike) ? storages.create(taskLike).put(taskLike, 1) : storages.create(taskLike).put(taskLike, storages.create(taskLike).get(taskLike) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                                    }
                                                                    break
                                                                }
                                                                // 如果是第一次记录时间，显示倒计时信息
                                                                0 == timeRecord1 && (log(restartTime(storages.create("recordLikeTime").get("recordLikeTime")) + " 后执行点赞"), timeRecord1 = 1)
                                                            // 如果任务类型为2（图文）且图文开关已开启
                                                            } else if (2 == randomNumber && storages.create("switchImageText").get("switchImageText")) {
                                                                // 如果没有图文时间记录，立即执行图文任务
                                                                if (null == storages.create("recordImageTextTime").get("recordImageTextTime")) {
                                                                    if (log("图文1"), imageText()) {
                                                                        // 图文成功后更新任务状态，并向服务器报告任务重启检查（当任务数量达到重启阈值时）
                                                                        // 更新任务执行状态、移除已完成任务、短暂休眠、更新任务数组、更新总计数器和图文计数器、检查重启条件
                                                                        if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskImageText).get(taskImageText) ? storages.create(taskImageText).put(taskImageText, 1) : storages.create(taskImageText).put(taskImageText, storages.create(taskImageText).get(taskImageText) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                                    }
                                                                    break
                                                                }
                                                                // 如果已到达预定的图文时间，执行图文任务
                                                                if ((new Date).getTime() >= storages.create("recordImageTextTime").get("recordImageTextTime")) {
                                                                    if (log("图文2"), imageText()) {
                                                                        // 图文成功后更新任务状态，并向服务器报告任务重启检查（当任务数量达到重启阈值时）
                                                                        // 更新任务执行状态、移除已完成任务、短暂休眠、更新任务数组、更新总计数器和图文计数器、检查重启条件
                                                                        if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskImageText).get(taskImageText) ? storages.create(taskImageText).put(taskImageText, 1) : storages.create(taskImageText).put(taskImageText, storages.create(taskImageText).get(taskImageText) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                                    }
                                                                    break
                                                                }
                                                                // 如果是第一次记录时间，显示倒计时信息
                                                                0 == timeRecord2 && (log(restartTime(storages.create("recordImageTextTime").get("recordImageTextTime")) + " 后执行图文"), timeRecord2 = 1)
                                                            // 如果任务类型为3（头像点击）且头像开关已开启
                                                            } else if (3 == randomNumber && storages.create("switchProfilePicture").get("switchProfilePicture")) {
                                                                // 如果没有头像时间记录，立即执行头像点击任务
                                                                if (null == storages.create("recordProfilePictureTime").get("recordProfilePictureTime")) {
                                                                    if (log("头像"), profilePicture(whatHao)) {
                                                                        // 头像成功后更新任务状态，并向服务器报告任务重启检查（当任务数量达到重启阈值时）
                                                                        // 更新任务执行状态、移除已完成任务、短暂休眠、更新任务数组、更新总计数器和头像计数器、检查重启条件
                                                                        if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskProfilePicture).get(taskProfilePicture) ? storages.create(taskProfilePicture).put(taskProfilePicture, 1) : storages.create(taskProfilePicture).put(taskProfilePicture, storages.create(taskProfilePicture).get(taskProfilePicture) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                                    }
                                                                    break
                                                                }
                                                                // 如果已到达预定的头像点击时间，执行头像点击任务
                                                                if ((new Date).getTime() >= storages.create("recordProfilePictureTime").get("recordProfilePictureTime")) {
                                                                    if (log("头像"), profilePicture(whatHao)) {
                                                                        // 头像成功后更新任务状态，并向服务器报告任务重启检查（当任务数量达到重启阈值时）
                                                                        // 更新任务执行状态、移除已完成任务、短暂休眠、更新任务数组、更新总计数器和头像计数器、检查重启条件
                                                                        if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskProfilePicture).get(taskProfilePicture) ? storages.create(taskProfilePicture).put(taskProfilePicture, 1) : storages.create(taskProfilePicture).put(taskProfilePicture, storages.create(taskProfilePicture).get(taskProfilePicture) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                                    }
                                                                    break
                                                                }
                                                                // 如果是第一次记录时间，显示倒计时信息
                                                                0 == timeRecord3 && (log(restartTime(storages.create("recordProfilePictureTime").get("recordProfilePictureTime")) + " 后执行头像"), timeRecord3 = 1)
                                                            } else if (4 == randomNumber && storages.create("switchCollect").get("switchCollect")) {
                                                                if (null == storages.create("recordCollectTime").get("recordCollectTime")) {
                                                                    if (log("收藏"), collect()) {
                                                                        // 收藏成功后更新任务状态，并向服务器报告任务重启检查（当任务数量达到重启阈值时）
                                                                        if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskCollect).get(taskCollect) ? storages.create(taskCollect).put(taskCollect, 1) : storages.create(taskCollect).put(taskCollect, storages.create(taskCollect).get(taskCollect) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                                    }
                                                                    break
                                                                }
                                                                if ((new Date).getTime() >= storages.create("recordCollectTime").get("recordCollectTime")) {
                                                                    if (log("收藏"), collect()) {
                                                                        // 收藏成功后更新任务状态，并向服务器报告任务重启检查（当任务数量达到重启阈值时）
                                                                        if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskCollect).get(taskCollect) ? storages.create(taskCollect).put(taskCollect, 1) : storages.create(taskCollect).put(taskCollect, storages.create(taskCollect).get(taskCollect) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                                    }
                                                                    break
                                                                }
                                                                0 == timeRecord4 && (log(restartTime(storages.create("recordCollectTime").get("recordCollectTime")) + " 后执行收藏"), timeRecord4 = 1)
                                                            } else if (5 == randomNumber && storages.create("switchComment").get("switchComment")) {
                                                                if (null == storages.create("recordCommentTime").get("recordCommentTime")) {
                                                                    if (log("评论"), comment(idName)) {
                                                                        // 评论成功后更新任务状态，并向服务器报告任务重启检查（当任务数量达到重启阈值时）
                                                                        if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskComment).get(taskComment) ? storages.create(taskComment).put(taskComment, 1) : storages.create(taskComment).put(taskComment, storages.create(taskComment).get(taskComment) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                                    }
                                                                    break
                                                                }
                                                                if ((new Date).getTime() >= storages.create("recordCommentTime").get("recordCommentTime")) {
                                                                    if (log("评论"), comment(idName)) {
                                                                        // 评论成功后更新任务状态，并向服务器报告任务重启检查（当任务数量达到重启阈值时）
                                                                        if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskComment).get(taskComment) ? storages.create(taskComment).put(taskComment, 1) : storages.create(taskComment).put(taskComment, storages.create(taskComment).get(taskComment) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                                    }
                                                                    break
                                                                }
                                                                0 == timeRecord5 && (log(restartTime(storages.create("recordCommentTime").get("recordCommentTime")) + " 后执行评论"), timeRecord5 = 1)
                                                            } else if (6 == randomNumber && storages.create("switchFollowWithInterest").get("switchFollowWithInterest")) {
                                                                if (null == storages.create("recordFollowWithInterestTime").get("recordFollowWithInterestTime")) {
                                                                    if (log("关注"), followWithInterest()) {
                                                                        // 关注成功后更新任务状态，并向服务器报告任务重启检查（当任务数量达到重启阈值时）
                                                                        if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskFollowWithInterest).get(taskFollowWithInterest) ? storages.create(taskFollowWithInterest).put(taskFollowWithInterest, 1) : storages.create(taskFollowWithInterest).put(taskFollowWithInterest, storages.create(taskFollowWithInterest).get(taskFollowWithInterest) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                                    }
                                                                    break
                                                                }
                                                                if ((new Date).getTime() >= storages.create("recordFollowWithInterestTime").get("recordFollowWithInterestTime")) {
                                                                    if (log("关注"), followWithInterest()) {
                                                                        // 关注成功后更新任务状态，并向服务器报告任务重启检查（当任务数量达到重启阈值时）
                                                                        if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskFollowWithInterest).get(taskFollowWithInterest) ? storages.create(taskFollowWithInterest).put(taskFollowWithInterest, 1) : storages.create(taskFollowWithInterest).put(taskFollowWithInterest, storages.create(taskFollowWithInterest).get(taskFollowWithInterest) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                                    }
                                                                    break
                                                                }
                                                                0 == timeRecord6 && (log(restartTime(storages.create("recordFollowWithInterestTime").get("recordFollowWithInterestTime")) + " 后执行关注"), timeRecord6 = 1)
                                                            } else if (7 == randomNumber && storages.create("switchPrivateLetter").get("switchPrivateLetter")) {
                                                                if (null == storages.create("recordPrivateLetterTime").get("recordPrivateLetterTime")) {
                                                                    if (log("私信"), privateLetter()) {
                                                                        // 私信成功后更新任务状态，并向服务器报告任务重启检查（当任务数量达到重启阈值时）
                                                                        if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), storages.create("jieliu").get("jieliu") && (null == storages.create(time() + "sendPrivateLetterCount_").get(time() + "sendPrivateLetterCount_") ? storages.create(time() + "sendPrivateLetterCount_").put(time() + "sendPrivateLetterCount_", 1) : storages.create(time() + "sendPrivateLetterCount_").put(time() + "sendPrivateLetterCount_", storages.create(time() + "sendPrivateLetterCount_").get(time() + "sendPrivateLetterCount_") + 1)), null == storages.create(taskPrivateLetter).get(taskPrivateLetter) ? storages.create(taskPrivateLetter).put(taskPrivateLetter, 1) : storages.create(taskPrivateLetter).put(taskPrivateLetter, storages.create(taskPrivateLetter).get(taskPrivateLetter) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                                    }
                                                                    break
                                                                }
                                                                if ((new Date).getTime() >= storages.create("recordPrivateLetterTime").get("recordPrivateLetterTime")) {
                                                                    if (log("私信"), privateLetter()) {
                                                                        if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), storages.create("jieliu").get("jieliu") && (null == storages.create(time() + "sendPrivateLetterCount_").get(time() + "sendPrivateLetterCount_") ? storages.create(time() + "sendPrivateLetterCount_").put(time() + "sendPrivateLetterCount_", 1) : storages.create(time() + "sendPrivateLetterCount_").put(time() + "sendPrivateLetterCount_", storages.create(time() + "sendPrivateLetterCount_").get(time() + "sendPrivateLetterCount_") + 1)), null == storages.create(taskPrivateLetter).get(taskPrivateLetter) ? storages.create(taskPrivateLetter).put(taskPrivateLetter, 1) : storages.create(taskPrivateLetter).put(taskPrivateLetter, storages.create(taskPrivateLetter).get(taskPrivateLetter) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                                    }
                                                                    break
                                                                }
                                                                0 == timeRecord7 && (log(restartTime(storages.create("recordPrivateLetterTime").get("recordPrivateLetterTime")) + " 后执行私信"), timeRecord7 = 1)
                                                            } else if (8 == randomNumber && storages.create("switchShare").get("switchShare")) {
                                                                if (null == storages.create("recordShareTime").get("recordShareTime")) {
                                                                    if (log("分享"), share()) {
                                                                        // 分享成功后更新任务状态，并向服务器报告任务重启检查（当任务数量达到重启阈值时）
                                                                        if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskShare).get(taskShare) ? storages.create(taskShare).put(taskShare, 1) : storages.create(taskShare).put(taskShare, storages.create(taskShare).get(taskShare) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                                    }
                                                                    break
                                                                }
                                                                if ((new Date).getTime() >= storages.create("recordShareTime").get("recordShareTime")) {
                                                                    if (log("分享"), share()) {
                                                                        if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskShare).get(taskShare) ? storages.create(taskShare).put(taskShare, 1) : storages.create(taskShare).put(taskShare, storages.create(taskShare).get(taskShare) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                                    }
                                                                    break
                                                                }
                                                                0 == timeRecord8 && (log(restartTime(storages.create("recordShareTime").get("recordShareTime")) + " 后执行分享"), timeRecord8 = 1)
                                                            } else if (9 == randomNumber && storages.create("switchClickComment").get("switchClickComment")) {
                                                                if (null == storages.create("recordClickCommentTime").get("recordClickCommentTime")) {
                                                                    if (log("点评"), clickComment()) {
                                                                        // 点评成功后更新任务状态，并向服务器报告任务重启检查（当任务数量达到重启阈值时）
                                                                        if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskClickComment).get(taskClickComment) ? storages.create(taskClickComment).put(taskClickComment, 1) : storages.create(taskClickComment).put(taskClickComment, storages.create(taskClickComment).get(taskClickComment) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                                    }
                                                                    break
                                                                }
                                                                if ((new Date).getTime() >= storages.create("recordClickCommentTime").get("recordClickCommentTime")) {
                                                                    if (log("点评"), clickComment()) {
                                                                        // 点评成功后更新任务状态，并向服务器报告任务重启检查（当任务数量达到重启阈值时）
                                                                        if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskClickComment).get(taskClickComment) ? storages.create(taskClickComment).put(taskClickComment, 1) : storages.create(taskClickComment).put(taskClickComment, storages.create(taskClickComment).get(taskClickComment) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                                    }
                                                                    break
                                                                }
                                                                0 == timeRecord9 && (log(restartTime(storages.create("recordClickCommentTime").get("recordClickCommentTime")) + " 后执行点评"), timeRecord9 = 1)
                                                            } else if (null == storages.create(time() + "arr").get(time() + "arr")) break
                                                        }
                                                    } else {
                                                        if (textContains("关闭应用").findOnce() && textContains("等待").findOnce()) {
                                                            var bound = textContains("等待").findOne().bounds(),
                                                                x = bound.centerX(),
                                                                y = bound.centerY();
                                                            return click(x, y), sleep(5e3), 1
                                                        }
                                                        if (null == storages.create("countNone").get("countNone") ? storages.create("countNone").put("countNone", 1) : storages.create("countNone").put("countNone", storages.create("countNone").get("countNone") + 1), storages.create("jieliu").get("jieliu")) {
                                                            if (storages.create("countNone").get("countNone") >= 30) return log("对标不好 更换对标"), 1
                                                        } else if (storages.create("countNone").get("countNone") >= 8) return log("对标不好 更换对标"), 1
                                                    } doTrue && storages.create("recordxinhun").put("recordxinhun", (new Date).getTime() + 1e3 * storages.create("duoshaomiao").get("duoshaomiao"))
                                            } else log(restartTime(storages.create("recordxinhun").get("recordxinhun")) + " 后执行任务");
                                    else {
                                        var doTrue = !1;
                                        if (textContains("关闭应用").findOnce() && textContains("等待").findOnce()) {
                                            var bound = textContains("等待").findOne().bounds(),
                                                x = bound.centerX(),
                                                y = bound.centerY();
                                            return click(x, y), sleep(5e3), 1
                                        }
                                        var taskName = time() + "arr",
                                            taskLike = time() + "recordLikeCount_convention",
                                            taskImageText = time() + "recordImageTextCount_convention",
                                            taskProfilePicture = time() + "recordProfilePictureCount_convention",
                                            taskCollect = time() + "recordCollectCount_convention",
                                            taskComment = time() + "recordCommentCount_convention",
                                            taskFollowWithInterest = time() + "recordFollowWithInterestCount_convention",
                                            taskPrivateLetter = time() + "recordPrivateLetterCount_convention",
                                            taskShare = time() + "recordShareCount_convention",
                                            taskClickComment = time() + "recordClickCommentCount_convention",
                                            taskAllCount = time() + "allCount";
                                        if (!text("暂无作品").findOnce() && !text("私密账号").findOnce())
                                            if (descContains("点赞数").findOne(8e3), storages.create("jiance").get("jiance") && storages.create("taskOfImageText").put("taskOfImageText", 0), storages.create("jiance").get("jiance") && storages.create("taskOfLike").put("taskOfLike", 0), meetTheOperatingConditions(whatHao, idName)) {
                                                if (textContains("关闭应用").findOnce() && textContains("等待").findOnce()) {
                                                    var bound = textContains("等待").findOne().bounds(),
                                                        x = bound.centerX(),
                                                        y = bound.centerY();
                                                    return click(x, y), sleep(5e3), 1
                                                }
                                                storages.create("countNone").put("countNone", 0), descContains("复制名字").findOnce() && storages.create("homepageName").put(descContains("复制名字").findOne().text(), 1);
                                                var arr = storages.create(taskName).get(taskName);
                                                if (null == arr) return 1;
                                                if (null != arr && 0 == arr.length) return 1;
                                                for (var timeRecord1 = 0, timeRecord2 = 0, timeRecord3 = 0, timeRecord4 = 0, timeRecord5 = 0, timeRecord6 = 0, timeRecord7 = 0, timeRecord8 = 0, timeRecord9 = 0;;) {
                                                    if (textContains("关闭应用").findOnce() && textContains("等待").findOnce()) {
                                                        var bound = textContains("等待").findOne().bounds(),
                                                            x = bound.centerX(),
                                                            y = bound.centerY();
                                                        return click(x, y), sleep(5e3), 1
                                                    }
                                                    if (storages.create("youxu").get("youxu")) {
                                                        var youxuArr = storages.create("youxuArr").get("youxuArr");
                                                        if (null == youxuArr || 0 == youxuArr.length) {
                                                            var yxArr = [storages.create("youxu1").get("youxu1"), storages.create("youxu2").get("youxu2"), storages.create("youxu3").get("youxu3"), storages.create("youxu4").get("youxu4"), storages.create("youxu5").get("youxu5"), storages.create("youxu6").get("youxu6"), storages.create("youxu7").get("youxu7"), storages.create("youxu8").get("youxu8"), storages.create("youxu9").get("youxu9"), storages.create("youxu10").get("youxu10"), storages.create("youxu11").get("youxu11"), storages.create("youxu12").get("youxu12"), storages.create("youxu13").get("youxu13"), storages.create("youxu14").get("youxu14"), storages.create("youxu15").get("youxu15")];
                                                            storages.create("youxuArr").put("youxuArr", yxArr)
                                                        }
                                                        var youxuArr = storages.create("youxuArr").get("youxuArr"),
                                                            serialNumber = returnSerialNumber(youxuArr);
                                                        10 == serialNumber && (alert("有序的配置有误", "检查输入是否正确"), exit());
                                                        var randomeIndex = returnArrIndex(serialNumber, arr),
                                                            randomNumber = arr[randomeIndex]
                                                    } else if (storages.create("jiance").get("jiance"))
                                                        if (storages.create("zanyan").get("zanyan")) var randomeIndex = random(0, arr.length - 1),
                                                            randomNumber = arr[randomeIndex];
                                                        else if (storages.create("xinyouxu").get("xinyouxu")) var randomeIndex = 0,
                                                        randomNumber = arr[randomeIndex];
                                                    else if (storages.create("huoli").get("huoli")) var randomeIndex = 0,
                                                        randomNumber = arr[randomeIndex];
                                                    else if (storages.create("shunxu").get("shunxu")) var randomeIndex = 0,
                                                        randomNumber = arr[randomeIndex];
                                                    else if (storages.create("switchImageText").get("switchImageText") && storages.create("imageText").get("imageText") > 0)
                                                        if (null == storages.create("imageTextNextTimes").get("imageTextNextTimes") || (new Date).getTime() >= storages.create("imageTextNextTimes").get("imageTextNextTimes"))
                                                            for (;;) {
                                                                var randomeIndex = random(0, arr.length - 1),
                                                                    randomNumber = arr[randomeIndex];
                                                                if (2 == randomNumber) break;
                                                                if (null == storages.create(time() + "arr").get(time() + "arr")) return 1
                                                            } else if (storages.create("switchLike").get("switchLike") && storages.create("like").get("like") > 0)
                                                                if (null == storages.create("recordLikeTime").get("recordLikeTime") || (new Date).getTime() >= storages.create("recordLikeTime").get("recordLikeTime"))
                                                                    for (;;) {
                                                                        var randomeIndex = random(0, arr.length - 1),
                                                                            randomNumber = arr[randomeIndex];
                                                                        if (1 == randomNumber) break;
                                                                        if (null == storages.create(time() + "arr").get(time() + "arr")) return 1
                                                                    } else
                                                                        for (log(restartTime(storages.create("imageTextNextTimes").get("imageTextNextTimes")) + " 后执行图文"), log(restartTime(storages.create("recordLikeTime").get("recordLikeTime")) + " 后执行点赞");;) {
                                                                            var randomeIndex = random(0, arr.length - 1),
                                                                                randomNumber = arr[randomeIndex];
                                                                            if (1 != randomNumber && 2 != randomNumber) break;
                                                                            if (null == storages.create("imageTextNextTimes").get("imageTextNextTimes") || (new Date).getTime() >= storages.create("imageTextNextTimes").get("imageTextNextTimes")) {
                                                                                for (;;) {
                                                                                    var randomeIndex = random(0, arr.length - 1),
                                                                                        randomNumber = arr[randomeIndex];
                                                                                    if (2 == randomNumber) break;
                                                                                    if (null == storages.create(time() + "arr").get(time() + "arr")) return 1
                                                                                }
                                                                                break
                                                                            }
                                                                            if (null == storages.create("recordLikeTime").get("recordLikeTime") || (new Date).getTime() >= storages.create("recordLikeTime").get("recordLikeTime")) {
                                                                                for (;;) {
                                                                                    var randomeIndex = random(0, arr.length - 1),
                                                                                        randomNumber = arr[randomeIndex];
                                                                                    if (1 == randomNumber) break;
                                                                                    if (null == storages.create(time() + "arr").get(time() + "arr")) return 1
                                                                                }
                                                                                break
                                                                            }
                                                                        } else var randomeIndex = random(0, arr.length - 1),
                                                                            randomNumber = arr[randomeIndex];
                                                                else if (storages.create("switchLike").get("switchLike") && storages.create("like").get("like") > 0)
                                                        if (null == storages.create("recordLikeTime").get("recordLikeTime") || (new Date).getTime() >= storages.create("recordLikeTime").get("recordLikeTime"))
                                                            for (;;) {
                                                                var randomeIndex = random(0, arr.length - 1),
                                                                    randomNumber = arr[randomeIndex];
                                                                if (1 == randomNumber) break;
                                                                if (null == storages.create(time() + "arr").get(time() + "arr")) return 1
                                                            } else
                                                                for (log(restartTime(storages.create("recordLikeTime").get("recordLikeTime")) + " 后执行点赞");;) {
                                                                    var randomeIndex = random(0, arr.length - 1),
                                                                        randomNumber = arr[randomeIndex];
                                                                    if (1 != randomNumber) break;
                                                                    if (null == storages.create("recordLikeTime").get("recordLikeTime") || (new Date).getTime() >= storages.create("recordLikeTime").get("recordLikeTime")) {
                                                                        for (;;) {
                                                                            var randomeIndex = random(0, arr.length - 1),
                                                                                randomNumber = arr[randomeIndex];
                                                                            if (1 == randomNumber) break;
                                                                            if (null == storages.create(time() + "arr").get(time() + "arr")) return 1
                                                                        }
                                                                        break
                                                                    }
                                                                } else var randomeIndex = random(0, arr.length - 1),
                                                                    randomNumber = arr[randomeIndex];
                                                        else if (storages.create("huoli").get("huoli")) var randomeIndex = 0,
                                                        randomNumber = arr[randomeIndex];
                                                    else if (storages.create("xinyouxu").get("xinyouxu")) var randomeIndex = 0,
                                                        randomNumber = arr[randomeIndex];
                                                    else if (storages.create("zanyan").get("zanyan")) var randomeIndex = random(0, arr.length - 1),
                                                        randomNumber = arr[randomeIndex];
                                                    else if (storages.create("shunxu").get("shunxu")) var randomeIndex = 0,
                                                        randomNumber = arr[randomeIndex];
                                                    else var randomeIndex = random(0, arr.length - 1),
                                                        randomNumber = arr[randomeIndex];
                                                    // 新婚模式下的任务执行逻辑（与检测模式类似但有所区别）
                                                    // 如果任务类型为1（点赞）且点赞开关已开启
                                                    if (1 == randomNumber && storages.create("switchLike").get("switchLike")) {
                                                        // 如果没有点赞时间记录，立即执行点赞
                                                        if (null == storages.create("recordLikeTime").get("recordLikeTime")) {
                                                            if (log("点赞3"), like()) {
                                                                // 点赞成功后更新任务状态和计数器，检查重启条件
                                                                if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskLike).get(taskLike) ? storages.create(taskLike).put(taskLike, 1) : storages.create(taskLike).put(taskLike, storages.create(taskLike).get(taskLike) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                            }
                                                            break
                                                        }
                                                        // 如果已到达预定的点赞时间，执行点赞
                                                        if ((new Date).getTime() >= storages.create("recordLikeTime").get("recordLikeTime")) {
                                                            if (log("点赞4"), like()) {
                                                                // 点赞成功后更新任务状态和计数器，检查重启条件
                                                                if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskLike).get(taskLike) ? storages.create(taskLike).put(taskLike, 1) : storages.create(taskLike).put(taskLike, storages.create(taskLike).get(taskLike) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                            }
                                                            break
                                                        }
                                                        // 如果是第一次记录时间，显示倒计时信息
                                                        0 == timeRecord1 && (log(restartTime(storages.create("recordLikeTime").get("recordLikeTime")) + " 后执行点赞"), timeRecord1 = 1)
                                                    // 如果任务类型为2（图文）且图文开关已开启
                                                    } else if (2 == randomNumber && storages.create("switchImageText").get("switchImageText")) {
                                                        // 如果没有图文时间记录，立即执行图文任务
                                                        if (null == storages.create("recordImageTextTime").get("recordImageTextTime")) {
                                                            if (log("图文3"), imageText()) {
                                                                // 图文成功后更新任务状态和计数器，检查重启条件
                                                                if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskImageText).get(taskImageText) ? storages.create(taskImageText).put(taskImageText, 1) : storages.create(taskImageText).put(taskImageText, storages.create(taskImageText).get(taskImageText) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                            }
                                                            break
                                                        }
                                                        // 如果已到达预定的图文时间，执行图文任务
                                                        if ((new Date).getTime() >= storages.create("recordImageTextTime").get("recordImageTextTime")) {
                                                            if (log("图文4"), imageText()) {
                                                                // 图文成功后更新任务状态和计数器，检查重启条件
                                                                if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskImageText).get(taskImageText) ? storages.create(taskImageText).put(taskImageText, 1) : storages.create(taskImageText).put(taskImageText, storages.create(taskImageText).get(taskImageText) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                            }
                                                            break
                                                        }
                                                        // 如果是第一次记录时间，显示倒计时信息
                                                        0 == timeRecord2 && (log(restartTime(storages.create("recordImageTextTime").get("recordImageTextTime")) + " 后执行图文"), timeRecord2 = 1)
                                                    } else if (3 == randomNumber && storages.create("switchProfilePicture").get("switchProfilePicture")) {
                                                        if (null == storages.create("recordProfilePictureTime").get("recordProfilePictureTime")) {
                                                            if (log("头像"), profilePicture(whatHao)) {
                                                                if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskProfilePicture).get(taskProfilePicture) ? storages.create(taskProfilePicture).put(taskProfilePicture, 1) : storages.create(taskProfilePicture).put(taskProfilePicture, storages.create(taskProfilePicture).get(taskProfilePicture) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                            }
                                                            break
                                                        }
                                                        if ((new Date).getTime() >= storages.create("recordProfilePictureTime").get("recordProfilePictureTime")) {
                                                            if (log("头像"), profilePicture(whatHao)) {
                                                                if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskProfilePicture).get(taskProfilePicture) ? storages.create(taskProfilePicture).put(taskProfilePicture, 1) : storages.create(taskProfilePicture).put(taskProfilePicture, storages.create(taskProfilePicture).get(taskProfilePicture) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                            }
                                                            break
                                                        }
                                                        0 == timeRecord3 && (log(restartTime(storages.create("recordProfilePictureTime").get("recordProfilePictureTime")) + " 后执行头像"), timeRecord3 = 1)
                                                    } else if (4 == randomNumber && storages.create("switchCollect").get("switchCollect")) {
                                                        if (null == storages.create("recordCollectTime").get("recordCollectTime")) {
                                                            if (log("收藏"), collect()) {
                                                                if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskCollect).get(taskCollect) ? storages.create(taskCollect).put(taskCollect, 1) : storages.create(taskCollect).put(taskCollect, storages.create(taskCollect).get(taskCollect) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                            }
                                                            break
                                                        }
                                                        if ((new Date).getTime() >= storages.create("recordCollectTime").get("recordCollectTime")) {
                                                            if (log("收藏"), collect()) {
                                                                if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskCollect).get(taskCollect) ? storages.create(taskCollect).put(taskCollect, 1) : storages.create(taskCollect).put(taskCollect, storages.create(taskCollect).get(taskCollect) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                            }
                                                            break
                                                        }
                                                        0 == timeRecord4 && (log(restartTime(storages.create("recordCollectTime").get("recordCollectTime")) + " 后执行收藏"), timeRecord4 = 1)
                                                    } else if (5 == randomNumber && storages.create("switchComment").get("switchComment")) {
                                                        if (null == storages.create("recordCommentTime").get("recordCommentTime")) {
                                                            if (log("评论"), comment(idName)) {
                                                                if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskComment).get(taskComment) ? storages.create(taskComment).put(taskComment, 1) : storages.create(taskComment).put(taskComment, storages.create(taskComment).get(taskComment) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                            }
                                                            break
                                                        }
                                                        if ((new Date).getTime() >= storages.create("recordCommentTime").get("recordCommentTime")) {
                                                            if (log("评论"), comment(idName)) {
                                                                if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskComment).get(taskComment) ? storages.create(taskComment).put(taskComment, 1) : storages.create(taskComment).put(taskComment, storages.create(taskComment).get(taskComment) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                            }
                                                            break
                                                        }
                                                        0 == timeRecord5 && (log(restartTime(storages.create("recordCommentTime").get("recordCommentTime")) + " 后执行评论"), timeRecord5 = 1)
                                                    } else if (6 == randomNumber && storages.create("switchFollowWithInterest").get("switchFollowWithInterest")) {
                                                        if (null == storages.create("recordFollowWithInterestTime").get("recordFollowWithInterestTime")) {
                                                            if (log("关注"), followWithInterest()) {
                                                                if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskFollowWithInterest).get(taskFollowWithInterest) ? storages.create(taskFollowWithInterest).put(taskFollowWithInterest, 1) : storages.create(taskFollowWithInterest).put(taskFollowWithInterest, storages.create(taskFollowWithInterest).get(taskFollowWithInterest) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                            }
                                                            break
                                                        }
                                                        if ((new Date).getTime() >= storages.create("recordFollowWithInterestTime").get("recordFollowWithInterestTime")) {
                                                            if (log("关注"), followWithInterest()) {
                                                                if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskFollowWithInterest).get(taskFollowWithInterest) ? storages.create(taskFollowWithInterest).put(taskFollowWithInterest, 1) : storages.create(taskFollowWithInterest).put(taskFollowWithInterest, storages.create(taskFollowWithInterest).get(taskFollowWithInterest) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                            }
                                                            break
                                                        }
                                                        0 == timeRecord6 && (log(restartTime(storages.create("recordFollowWithInterestTime").get("recordFollowWithInterestTime")) + " 后执行关注"), timeRecord6 = 1)
                                                    } else if (7 == randomNumber && storages.create("switchPrivateLetter").get("switchPrivateLetter")) {
                                                        if (null == storages.create("recordPrivateLetterTime").get("recordPrivateLetterTime")) {
                                                            if (log("私信"), privateLetter()) {
                                                                if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), storages.create("jieliu").get("jieliu") && (null == storages.create(time() + "sendPrivateLetterCount_").get(time() + "sendPrivateLetterCount_") ? storages.create(time() + "sendPrivateLetterCount_").put(time() + "sendPrivateLetterCount_", 1) : storages.create(time() + "sendPrivateLetterCount_").put(time() + "sendPrivateLetterCount_", storages.create(time() + "sendPrivateLetterCount_").get(time() + "sendPrivateLetterCount_") + 1)), null == storages.create(taskPrivateLetter).get(taskPrivateLetter) ? storages.create(taskPrivateLetter).put(taskPrivateLetter, 1) : storages.create(taskPrivateLetter).put(taskPrivateLetter, storages.create(taskPrivateLetter).get(taskPrivateLetter) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                            }
                                                            break
                                                        }
                                                        if ((new Date).getTime() >= storages.create("recordPrivateLetterTime").get("recordPrivateLetterTime")) {
                                                            if (log("私信"), privateLetter()) {
                                                                if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), storages.create("jieliu").get("jieliu") && (null == storages.create(time() + "sendPrivateLetterCount_").get(time() + "sendPrivateLetterCount_") ? storages.create(time() + "sendPrivateLetterCount_").put(time() + "sendPrivateLetterCount_", 1) : storages.create(time() + "sendPrivateLetterCount_").put(time() + "sendPrivateLetterCount_", storages.create(time() + "sendPrivateLetterCount_").get(time() + "sendPrivateLetterCount_") + 1)), null == storages.create(taskPrivateLetter).get(taskPrivateLetter) ? storages.create(taskPrivateLetter).put(taskPrivateLetter, 1) : storages.create(taskPrivateLetter).put(taskPrivateLetter, storages.create(taskPrivateLetter).get(taskPrivateLetter) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                            }
                                                            break
                                                        }
                                                        0 == timeRecord7 && (log(restartTime(storages.create("recordPrivateLetterTime").get("recordPrivateLetterTime")) + " 后执行私信"), timeRecord7 = 1)
                                                    } else if (8 == randomNumber && storages.create("switchShare").get("switchShare")) {
                                                        if (null == storages.create("recordShareTime").get("recordShareTime")) {
                                                            if (log("分享"), share()) {
                                                                if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskShare).get(taskShare) ? storages.create(taskShare).put(taskShare, 1) : storages.create(taskShare).put(taskShare, storages.create(taskShare).get(taskShare) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                            }
                                                            break
                                                        }
                                                        if ((new Date).getTime() >= storages.create("recordShareTime").get("recordShareTime")) {
                                                            if (log("分享"), share()) {
                                                                if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskShare).get(taskShare) ? storages.create(taskShare).put(taskShare, 1) : storages.create(taskShare).put(taskShare, storages.create(taskShare).get(taskShare) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                            }
                                                            break
                                                        }
                                                        0 == timeRecord8 && (log(restartTime(storages.create("recordShareTime").get("recordShareTime")) + " 后执行分享"), timeRecord8 = 1)
                                                    } else if (9 == randomNumber && storages.create("switchClickComment").get("switchClickComment")) {
                                                        if (null == storages.create("recordClickCommentTime").get("recordClickCommentTime")) {
                                                            if (log("点评"), clickComment()) {
                                                                // 点评成功后更新任务状态，并向服务器报告任务重启检查（当任务数量达到重启阈值时）
                                                                if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskClickComment).get(taskClickComment) ? storages.create(taskClickComment).put(taskClickComment, 1) : storages.create(taskClickComment).put(taskClickComment, storages.create(taskClickComment).get(taskClickComment) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                            }
                                                            break
                                                        }
                                                        if ((new Date).getTime() >= storages.create("recordClickCommentTime").get("recordClickCommentTime")) {
                                                            if (log("点评"), clickComment()) {
                                                                // 点评成功后更新任务状态，并向服务器报告任务重启检查（当任务数量达到重启阈值时）
                                                                if (doTrue = !0, storages.create("youxu").get("youxu") && (youxuArr.splice(0, 1), storages.create("youxuArr").put("youxuArr", youxuArr)), arr.splice(randomeIndex, 1), sleep(100), storages.create(taskName).put(taskName, arr), null == storages.create(taskAllCount).get(taskAllCount) ? storages.create(taskAllCount).put(taskAllCount, 1) : storages.create(taskAllCount).put(taskAllCount, storages.create(taskAllCount).get(taskAllCount) + 1), null == storages.create(taskClickComment).get(taskClickComment) ? storages.create(taskClickComment).put(taskClickComment, 1) : storages.create(taskClickComment).put(taskClickComment, storages.create(taskClickComment).get(taskClickComment) + 1), storages.create(taskAllCount).get(taskAllCount) % restartCount == 0 && storages.create(taskAllCount).get(taskAllCount) % restartCount == 0) return 1;

                                                            }
                                                            break
                                                        }
                                                        0 == timeRecord9 && (log(restartTime(storages.create("recordClickCommentTime").get("recordClickCommentTime")) + " 后执行点评"), timeRecord9 = 1)
                                                    // 如果任务数组为空，跳出任务执行循环
                                                    } else if (null == storages.create(time() + "arr").get(time() + "arr")) break
                                                }
                                            // 如果新婚模式时间未到或未启用新婚模式
                                            } else {
                                                // 检查应用关闭提示
                                                if (textContains("关闭应用").findOnce() && textContains("等待").findOnce()) {
                                                    var bound = textContains("等待").findOne().bounds(),
                                                        x = bound.centerX(),
                                                        y = bound.centerY();
                                                    return click(x, y), sleep(5e3), 1
                                                }
                                                // 更新无操作计数器，检查对标质量
                                                if (null == storages.create("countNone").get("countNone") ? storages.create("countNone").put("countNone", 1) : storages.create("countNone").put("countNone", storages.create("countNone").get("countNone") + 1), storages.create("jieliu").get("jieliu")) {
                                                    // 如果启用节流模式且无操作次数达到30次，更换对标
                                                    if (storages.create("countNone").get("countNone") >= 30) return log("对标不好 更换对标"), 1
                                                // 如果未启用节流模式且无操作次数达到8次，更换对标
                                                } else if (storages.create("countNone").get("countNone") >= 8) return log("对标不好 更换对标"), 1
                                            }
                                    }
                                    // 返回操作循环，确保能正确返回到列表页面
                                    for (var backRecordCount = 0;;) {
                                       // 检查是否在用户主页（通过多个UI元素确认）
                                        if (id(idName + ":id/root_layout").findOnce() && descContains("头像").clickable(!0).findOnce() && id("android:id/text1").textContains("关注").findOnce() && id("android:id/text1").textContains("粉丝").findOnce()) {
                                            // 如果启用了检测模式，执行任务效果检测
                                            if (storages.create("jiance").get("jiance")) {
                                                // 图文任务检测
                                                if (1 == storages.create("taskOfImageText").get("taskOfImageText")) {
                                                    // 进入主页并检测点赞数变化
                                                    if (log("图文检测"), log("进入主页"), id(idName + ":id/root_layout").find().get(i).click(), descContains("点赞数").findOne(8e3), sleep(500), descContains("点赞数").findOnce())
                                                        // 遍历所有点赞数元素，找到符合条件的作品
                                                        for (var i$ = 0; i$ < descContains("点赞数").find().size(); i$++)
                                                            // 检查作品结构和是否为置顶作品
                                                            if (4 == descContains("点赞数").find().get(i$).parent().parent().childCount() && "置顶" !== descContains("点赞数").find().get(i$).parent().parent().child(2).text()) {
                                                                var likes = descContains("点赞数").find().get(i$).text();
                                                                // 比较点赞数判断操作是否成功
                                                                likes > storages.create("likesCount").get("likesCount") ? (log("点赞成功"), storages.create("recordLikesDo").put("recordLikesDo", 0)) : (log("点赞失败"), null == storages.create("recordLikesDo").get("recordLikesDo") && storages.create("recordLikesDo").put("recordLikesDo", 0), storages.create("recordLikesDo").put("recordLikesDo", storages.create("recordLikesDo").get("recordLikesDo") + 1), log("连续" + storages.create("recordLikesDo").get("recordLikesDo") + "次点赞失败"), storages.create("recordLikesDo").get("recordLikesDo") >= storages.create("lianxu").get("lianxu") && (log(`图文将在${storages.create("jiangxiuxi").get("jiangxiuxi")}分钟后`), log("再次尝试点赞"), device.keepScreenOn(), storages.create("recordLikesDo").put("recordLikesDo", 0), storages.create("imageTextNextTimes").put("imageTextNextTimes", (new Date).getTime() + 60 * storages.create("jiangxiuxi").get("jiangxiuxi") * 1e3)));
                                                                break
                                                            } log("检测完毕 返回列表"), back(), id(idName + ":id/root_layout").findOne(), sleep(3e3)
                                                }
                                                // 点赞任务检测
                                                if (1 == storages.create("taskOfLike").get("taskOfLike")) {
                                                    // 进入主页并检测指定位置作品的点赞数变化
                                                    if (log("点赞检测"), log("进入主页"), id(idName + ":id/root_layout").find().get(i).click(), descContains("点赞数").findOne(8e3), sleep(500), descContains("点赞数").findOnce()) {
                                                        // 等待直到找到指定位置的作品
                                                        for (; !(descContains("点赞数").find().size() > storages.create("worksPos").get("worksPos")););
                                                        var likes = descContains("点赞数").find().get(storages.create("worksPos").get("worksPos")).text();
                                                        // 比较点赞数判断操作是否成功，并处理连续失败情况
                                                        likes > storages.create("likesCount$").get("likesCount$") ? (log("点赞成功"), storages.create("recordLikesDo$").put("recordLikesDo$", 0)) : (log("点赞失败"), null == storages.create("recordLikesDo$").get("recordLikesDo$") && storages.create("recordLikesDo$").put("recordLikesDo$", 0), storages.create("recordLikesDo$").put("recordLikesDo$", storages.create("recordLikesDo$").get("recordLikesDo$") + 1), log("连续" + storages.create("recordLikesDo$").get("recordLikesDo$") + "次点赞失败"), storages.create("recordLikesDo$").get("recordLikesDo$") >= storages.create("lianxu").get("lianxu") && (log(`点赞将在${storages.create("jiangxiuxi").get("jiangxiuxi")}分钟后`), log("再次尝试点赞"), device.keepScreenOn(), storages.create("recordLikesDo$").put("recordLikesDo$", 0), storages.create("recordLikeTime").put("recordLikeTime", (new Date).getTime() + 60 * storages.create("jiangxiuxi").get("jiangxiuxi") * 1e3)))
                                                    }
                                                    log("检测完毕 返回列表"), back(), id(idName + ":id/root_layout").findOne(), sleep(3e3)
                                                    /*
                                                    // 点赞效果检测：比较当前点赞数与操作前的点赞数
likes > storages.create("likesCount$").get("likesCount$") ? 
// 点赞成功：当前点赞数大于操作前的点赞数
(log("点赞成功"), 
 // 重置连续失败计数器为0
 storages.create("recordLikesDo$").put("recordLikesDo$", 0)) : 
// 点赞失败：点赞数没有增加
(log("点赞失败"), 
 // 如果失败计数器不存在，初始化为0
 null == storages.create("recordLikesDo$").get("recordLikesDo$") && 
    storages.create("recordLikesDo$").put("recordLikesDo$", 0), 
 // 失败计数器加1，记录连续失败次数
 storages.create("recordLikesDo$").put("recordLikesDo$", storages.create("recordLikesDo$").get("recordLikesDo$") + 1), 
 // 输出连续失败次数日志
 log("连续" + storages.create("recordLikesDo$").get("recordLikesDo$") + "次点赞失败"), 
 // 检查是否达到连续失败阈值，如果达到则启动休息机制
 storages.create("recordLikesDo$").get("recordLikesDo$") >= storages.create("lianxu").get("lianxu") && 
    (// 输出休息时间提示日志
     log(`点赞将在${storages.create("jiangxiuxi").get("jiangxiuxi")}分钟后`), 
     log("再次尝试点赞"), 
     // 重置失败计数器，准备下一轮检测
     storages.create("recordLikesDo$").put("recordLikesDo$", 0), 
     // 设置点赞冷却时间：当前时间 + 配置的休息分钟数（转换为毫秒）
     // 60 * 分钟数 * 1000 = 毫秒数
     storages.create("recordLikeTime").put("recordLikeTime", (new Date).getTime() + 60 * storages.create("jiangxiuxi").get("jiangxiuxi") * 1e3)))*/
                                                }
                                            }
                                            // 检查是否出现应用关闭提示，如果有则点击等待按钮
                                            if (textContains("关闭应用").findOnce() && textContains("等待").findOnce()) {
                                                var bound = textContains("等待").findOne().bounds(),
                                                    x = bound.centerX(),
                                                    y = bound.centerY();
                                                return click(x, y), sleep(5e3), 1
                                            }
                                            // 如果设置了倒计时，执行倒计时等待
                                            if (countDown > 0)
                                                for (var i_c = countDown; i_c > 0; i_c--) log(i_c + "秒后开始"), sleep(1e3);
                                            break
                                        }
                                        // 处理验证码滑块，检查应用关闭提示
                                        if ((textContains("请完成下列验证后继续").findOnce() || descContains("请完成下列验证后继续").findOnce()) && slider(), textContains("关闭应用").findOnce() && textContains("等待").findOnce()) {
                                            var bound = textContains("等待").findOne().bounds(),
                                                x = bound.centerX(),
                                                y = bound.centerY();
                                            return click(x, y), sleep(5e3), 1
                                        }
                                        // 找到目标用户后，记录任务完成状态并执行返回操作
                                        // 根据检测模式和任务类型设置不同的返回等待时间
                                        if (textContains(whatHao).findOnce() && (doTrue && storages.create("didTask").put(textContains(whatHao).findOne().text(), 1), descContains("复制名字").findOne(500) && log(descContains("复制名字").findOne().text())), storages.create("jiance").get("jiance") ? 1 == storages.create("taskOfImageText").get("taskOfImageText") ? 1 == storages.create("taskOfImageText").get("taskOfImageText") ? (log("返回"), back(), 0 == backHold && (backHold = 5), sleep(1e3 * (backHold - 2))) : (log("返回"), back(), 0 == backHold && (backHold = 5), sleep(1e3 * backHold)) : 1 == storages.create("taskOfLike").get("taskOfLike") && 1 == storages.create("taskOfLike").get("taskOfLike") ? (log("返回"), back(), 0 == backHold && (backHold = 5), sleep(1e3 * (backHold - 2))) : (log("返回"), back(), 0 == backHold && (backHold = 5), sleep(1e3 * backHold)) : (log("返回"), back(), 0 == backHold && (backHold = 5), sleep(1e3 * backHold)), backRecordCount++, backRecordCount >= 10) return log("返回异常 重启"), 1
                                    }
                                } else {
                                    // 非喜欢模式的处理分支
                                    // 检查是否出现应用关闭提示
                                    if (textContains("关闭应用").findOnce() && textContains("等待").findOnce()) {
                                        var bound = textContains("等待").findOne().bounds(),
                                            x = bound.centerX(),
                                            y = bound.centerY();
                                        return click(x, y), sleep(5e3), 1
                                    }
                                    // 记录界面跟踪次数，如果达到界面元素数量则清除记录并返回状态码1
                                    if (null == storages.create("numberOfInterfaceTracesLeft").get("numberOfInterfaceTracesLeft") ? storages.create("numberOfInterfaceTracesLeft").put("numberOfInterfaceTracesLfet", 1) : storages.create("numberOfInterfaceTracesLeft").put("numberOfInterfaceTracesLfet", storages.create("numberOfInterfaceTracesLeft").get("numberOfInterfaceTracesLeft") + 1), storages.create("numberOfInterfaceTracesLeft").get("numberOfInterfaceTracesLeft") == id(idName + ":id/root_layout").find().size()) return storages.create("numberOfInterfaceTracesLeft").remove("numberOfInterfaceTracesLeft"), 1
                                }
                        }
                }

                /**
                 * 获取当前日期的格式化字符串
                 * @returns {string} 格式化的日期字符串 (YYYYMMDD)
                 */
                function time() {
                    var e = (new Date).getFullYear();
                    if ((new Date).getMonth() + 1 < 10) var t = "0" + ((new Date).getMonth() + 1);
                    else t = (new Date).getMonth() + 1;
                    if ((new Date).getDate() < 10) var r = "0" + (new Date).getDate();
                    else r = (new Date).getDate();
                    return (new Date).getHours(), (new Date).getHours(), e + "" + t + r
                }

                /**
                 * 获取昨天的日期格式化字符串
                 * @returns {string} 昨天的日期字符串 (YYYYMMDD)
                 */
                function removeYestoday() {
                    yestoday = new Date(new Date - 864e5);
                    var e = yestoday.getFullYear();
                    if (yestoday.getMonth() + 1 < 10) var t = "0" + (yestoday.getMonth() + 1);
                    else t = yestoday.getMonth() + 1;
                    if (yestoday.getDate() < 10) var r = "0" + yestoday.getDate();
                    else r = yestoday.getDate();
                    return e + "" + t + r
                }

                /**
                 * 执行任务的主循环函数
                 * @param {string} idName - 应用ID名称
                 * @param {string} whatHao - 目标账号
                 * @param {number} restartCount - 重启计数
                 * @param {number} countDown - 倒计时
                 * @param {number} seconds - 基础间隔秒数
                 * @param {number} seconds_comment - 评论间隔秒数
                 * @param {number} seconds_like - 点赞间隔秒数
                 * @param {number} seconds_privateLetter - 私信间隔秒数
                 * @param {number} seconds_followWithInterest - 关注间隔秒数
                 * @param {number} seconds_collect - 收藏间隔秒数
                 * @param {number} seconds_profilePicture - 头像点击间隔秒数
                 * @param {number} seconds_imageText - 图文间隔秒数
                 * @param {number} backHold - 返回等待时间
                 * @param {number} seconds_clickComment - 点评间隔秒数
                 * @param {string} carmine - 胭脂红参数
                 */
                function performTasks(idName, whatHao, restartCount, countDown, seconds, seconds_comment, seconds_like, seconds_privateLetter, seconds_followWithInterest, seconds_collect, seconds_profilePicture, seconds_imageText, backHold, seconds_clickComment, carmine) {
                    for (;;) {
                        if (1 == traverseList(idName, whatHao, restartCount, countDown, seconds, seconds_comment, seconds_like, seconds_privateLetter, seconds_followWithInterest, seconds_collect, seconds_profilePicture, seconds_imageText, backHold, seconds_clickComment, carmine)) return;
                        if (storages.create("xihuan").get("xihuan"))(textContains("请完成下列验证后继续").findOnce() || descContains("请完成下列验证后继续").findOnce()) && slider(), log("翻页"), swipe(device.width / 2, device.height / 2, device.width / 2, 0, 100), sleep(3e3);
                        else {
                            if (storages.create("qishui").get("qishui") && text("加载更多").findOne(500) && (text("加载更多").findOne().parent().click(), sleep(15e3), slideTheNextPage()), storages.create("qishui").get("qishui")) {
                                var size = text("关注").clickable(!0).find().size();
                                // 向服务器请求验证当前页面关注按钮数量是否符合翻页条件
                                let result = size > 3; //eval(serverRequest("performTasks_2", size));
                                result ? slideTheNextPage() : (back(), sleep(5e3))
                            } else {
                                if (log("下一页"), slideTheNextPage(), textContains("关闭应用").findOnce() && textContains("等待").findOnce()) return;
                                if (id(idName + ":id/root_layout").findOnce(0)) {
                                    let e = id(idName + ":id/root_layout").findOnce(0).desc().split(",")[0];
                                    if (e == storages.create("nickName$").get("nickName$")) return void storages.create("nickName$").remove("nickName$");
                                    storages.create("nickName$").put("nickName$", e)
                                }
                            }
                            if (storages.create("qishui").get("qishui") && text("加载更多").findOne(500) && (text("加载更多").findOne().parent().click(), sleep(15e3), slideTheNextPage()), textContains("加载失败").findOnce()) return;
                            if (textContains("部分用户").findOnce() || textContains("暂时没有更多了").findOnce()) return
                        }
                    }
                }
                convention.convention = function(carmine) {
                    try {

                            let pjysdk3 = require("./PJYSDK.js");

                        /* 将pjysdk.js文件中的代码复制粘贴到上面 */

                        // AppKey 和 AppSecret 在泡椒云开发者后台获取
                        let pjysdk = new pjysdk3("d1v9d6jdqusvqakj9a4g", "YDKnwvy14aIyZ7SJSbctUALz66tqJucS");
                        pjysdk.debug = true;
                        pjysdk.SetCard(carmine);


                        // 监听心跳失败事件
                        pjysdk.event.on("heartbeat_failed", function(hret) {
                            toastLog(hret.message);
                            if (hret.code === 10214) {
                                sleep(200);
                                //exit();  // 退出脚本
                                toastLog("心跳失败1");
                                return
                            }
                            log("心跳失败，尝试重登...")
                            sleep(2000);
                            let login_ret;
                            var thread = threads.start(function() {
                                login_ret = pjysdk.CardLogin();
                            });
                            thread.join();
                            if (login_ret.code == 0) {
                                log("重登成功");
                            } else {
                                toastLog(login_ret.message); // 重登失败
                                sleep(200);
                                //exit();  // 退出脚本
                                toastLog("心跳失败2");
                            }
                        });
                        
                        
                        var thread = threads.start(function() {
                        

                            login_ret = pjysdk.CardLogin();
                        });
                       
                        thread.join();
                        if (login_ret.code == 0) {
                       
                            storages.create('stupid').put('stupid', true);
                            storages.create("whileTime").put("whileTime", *************);
                            //toastLog(getNumber());

                                if (null != storages.create(time() + "arr").get(time() + "arr")) {
                                    log("c1");
                                    // 向服务器请求验证今日任务数量是否已达上限
                                    //let result = eval(serverRequest("convention_1", storages.create(time() + "arr").get(time() + "arr").length));
                                    //修改1.0 应该是服务器记录每一次接口的执行数 判断今日次数是否执行完毕
                                    //if (!storages.create("liuhen").get("liuhen")) {
                                    //if (result) return log("今日次数已执行完毕"), void sleep(3e3);
                                    log("今日还可操作" + storages.create(time() + "arr").get(time() + "arr").length + "次")
                                    //}
                                } else {
                                    log("c1");
                                    storages.create(removeYestoday() + "arr").remove(removeYestoday() + "arr"), storages.create(time() + "likeLength_convention").put(time() + "likeLength_convention", storages.create("like").get("like")), storages.create(time() + "imageTextLength_convention").put(time() + "imageTextLength_convention", storages.create("imageText").get("imageText")), storages.create(time() + "profilePictureLength_convention").put(time() + "profilePictureLength_convention", storages.create("profilePicture").get("profilePicture")), storages.create(time() + "collectLength_convention").put(time() + "collectLength_convention", storages.create("collect").get("collect")), storages.create(time() + "commentLength_convention").put(time() + "commentLength_convention", storages.create("comment").get("comment")), storages.create(time() + "followWithInterestLength_convention").put(time() + "followWithInterestLength_convention", storages.create("followWithInterest").get("followWithInterest")), storages.create(time() + "privateLetterLength_convention").put(time() + "privateLetterLength_convention", storages.create("privateLetter").get("privateLetter")), storages.create(time() + "shareLength_convention").put(time() + "shareLength_convention", storages.create("share").get("share")), storages.create(time() + "clickCommentLength_convention").put(time() + "clickCommentLength_convention", storages.create("clickComment").get("clickComment"));
                                    var arr = eval(assignTasks());
                                    storages.create(time() + "arr").put(time() + "arr", arr)
                                }
                                if (storages.create("shunxu").get("shunxu")) {
                                    if (null != storages.create(time() + "arr").get(time() + "arr")) {
                                        let e = shunxufenzu(storages.create(time() + "arr").get(time() + "arr"));
                                        storages.create(time() + "arr").put(time() + "arr", e)
                                    }
                                } else if (null != storages.create(time() + "arr").get(time() + "arr")) {
                                    let e = youxufenzu(storages.create(time() + "arr").get(time() + "arr"));
                                    storages.create(time() + "arr").put(time() + "arr", e)
                                } {
                                    let e, t, r, a, s, n, i, o, l;
                                    if (storages.create("switchLike").get("switchLike")) e = storages.create(time() + "likeLength_convention").get(time() + "likeLength_convention");
                                    else {
                                        let t = storages.create(time() + "arr").get(time() + "arr");
                                        for (let e = t.length - 1; e >= 0; e--) 1 == t[e] && t.splice(e, 1);
                                        storages.create(time() + "arr").put(time() + "arr", t), e = 0
                                    }
                                    if (storages.create("switchImageText").get("switchImageText")) {
                                        t = storages.create(time() + "imageTextLength_convention").get(time() + "imageTextLength_convention");
                                        //log(t);
                                    } else {
                                        let e = storages.create(time() + "arr").get(time() + "arr");
                                        for (let t = e.length - 1; t >= 0; t--) 2 == e[t] && e.splice(t, 1);
                                        storages.create(time() + "arr").put(time() + "arr", e), t = 0
                                    }
                                    if (storages.create("switchProfilePicture").get("switchProfilePicture")) r = storages.create(time() + "profilePictureLength_convention").get(time() + "profilePictureLength_convention");
                                    else {
                                        let e = storages.create(time() + "arr").get(time() + "arr");
                                        for (let t = e.length - 1; t >= 0; t--) 3 == e[t] && e.splice(t, 1);
                                        storages.create(time() + "arr").put(time() + "arr", e), r = 0
                                    }
                                    if (storages.create("switchCollect").get("switchCollect")) a = storages.create(time() + "collectLength_convention").get(time() + "collectLength_convention");
                                    else {
                                        let e = storages.create(time() + "arr").get(time() + "arr");
                                        for (let t = e.length - 1; t >= 0; t--) 4 == e[t] && e.splice(t, 1);
                                        storages.create(time() + "arr").put(time() + "arr", e), a = 0
                                    }
                                    if (storages.create("switchComment").get("switchComment")) s = storages.create(time() + "commentLength_convention").get(time() + "commentLength_convention");
                                    else {
                                        let e = storages.create(time() + "arr").get(time() + "arr");
                                        for (let t = e.length - 1; t >= 0; t--) 5 == e[t] && e.splice(t, 1);
                                        storages.create(time() + "arr").put(time() + "arr", e), s = 0
                                    }
                                    if (storages.create("switchFollowWithInterest").get("switchFollowWithInterest")) n = storages.create(time() + "followWithInterestLength_convention").get(time() + "followWithInterestLength_convention");
                                    else {
                                        let e = storages.create(time() + "arr").get(time() + "arr");
                                        for (let t = e.length - 1; t >= 0; t--) 6 == e[t] && e.splice(t, 1);
                                        storages.create(time() + "arr").put(time() + "arr", e), n = 0
                                    }
                                    if (storages.create("switchPrivateLetter").get("switchPrivateLetter")) i = storages.create(time() + "privateLetterLength_convention").get(time() + "privateLetterLength_convention");
                                    else {
                                        let e = storages.create(time() + "arr").get(time() + "arr");
                                        for (let t = e.length - 1; t >= 0; t--) 7 == e[t] && e.splice(t, 1);
                                        storages.create(time() + "arr").put(time() + "arr", e), i = 0
                                    }
                                    if (storages.create("switchShare").get("switchShare")) o = storages.create(time() + "shareLength_convention").get(time() + "shareLength_convention");
                                    else {
                                        let e = storages.create(time() + "arr").get(time() + "arr");
                                        for (let t = e.length - 1; t >= 0; t--) 8 == e[t] && e.splice(t, 1);
                                        storages.create(time() + "arr").put(time() + "arr", e), o = 0
                                    }
                                    if (storages.create("switchClickComment").get("switchClickComment")) l = storages.create(time() + "clickCommentLength_convention").get(time() + "clickCommentLength_convention");
                                    else {
                                        let e = storages.create(time() + "arr").get(time() + "arr");
                                        for (let t = e.length - 1; t >= 0; t--) 9 == e[t] && e.splice(t, 1);
                                        storages.create(time() + "arr").put(time() + "arr", e), l = 0
                                    }
                                    storages.create("allTaskLength_convention").put("allTaskLength_convention", e + t + r + a + s + n + i + o + l)
                                    //log("all"+storages.create("allTaskLength_convention").get("allTaskLength_convention"));
                                }
                                // 检查留痕模式和任务数组状态，如果未启用留痕且任务数组为空或不存在，则等待5秒后返回
                                if (!storages.create("liuhen").get("liuhen") && (null == storages.create(time() + "arr").get(time() + "arr", arr) || 0 == storages.create(time() + "arr").get(time() + "arr", arr).length)) return log("0"), void sleep(5e3);

                                // 启动抖音应用并读取粉丝数量，然后根据模式执行不同的操作流程
                                if (log("t2"), turnOnTiktok(storages.create("appName").get("appName")), log("r32"), readFanCount(storages.create("whatHao").get("whatHao"), storages.create("idName").get("idName")), storages.create("qishui").get("qishui"))
                                    // 汽水模式：直接执行屏幕操作一和屏幕操作二
                                    log("qs4"), screenOne(storages.create("idName").get("idName")), log("qs5"), screenTwo(storages.create("idName").get("idName"));
                                else if (storages.create("selfSearching").get("selfSearching")) {
                                    // 自寻模式：根据粉丝增长情况决定执行策略
                                    log("c2");
                                    // 自寻按钮勾选确认后 向服务器请求验证粉丝差值是否符合自寻条件
                                    //修改2 粉丝增长大于等于20开始自寻
                                    let result = storages.create("fanDifference").get("fanDifference") >= 20; //eval(serverRequest("convention_2", storages.create("fanDifference").get("fanDifference")));
                                    if (result) {
                                        
                                        // 粉丝增长达到阈值，执行自寻操作（屏幕操作一和二）
                                        if (log("粉丝增长："+storages.create("fanDifference").get("fanDifference")), log("s6"), 1 == screenOne(storages.create("idName").get("idName"))) return;
                                        if (log("s7"), 1 == screenTwo(storages.create("idName").get("idName"))) return
                                    } else
                                        // 粉丝增长未达到阈值，执行常规搜索流程
                                        log(storages.create("fanDifference").get("fanDifference")), log("e82"), enterTheSearchInterface(storages.create("idName").get("idName")), log("e92"), enterConventionDateToEnterFanList(storages.create("idName").get("idName"), carmine)
                                } else
                                    // 普通模式：进入搜索界面并执行常规任务流程
                                    log("e10"), enterTheSearchInterface(storages.create("idName").get("idName")), log("e11"), enterConventionDateToEnterFanList(storages.create("idName").get("idName"), carmine);

                                // 执行主要任务：传入所有配置参数，包括各种操作的时间间隔和开关状态
                                log("p12"), performTasks(
                                    storages.create("idName").get("idName"),                    // 应用ID名称
                                    storages.create("whatHao").get("whatHao"),                  // 目标抖音号
                                    storages.create("restartCount").get("restartCount"),        // 重启计数
                                    storages.create("cgtl").get("cgtl"),                        // 查看评论时间间隔
                                    storages.create("sharetl").get("sharetl"),                  // 分享时间间隔
                                    storages.create("commenttl").get("commenttl"),              // 评论时间间隔
                                    storages.create("liketl").get("liketl"),                    // 点赞时间间隔
                                    storages.create("privateLettertl").get("privateLettertl"), // 私信时间间隔
                                    storages.create("followWithInteresttl").get("followWithInteresttl"), // 关注时间间隔
                                    storages.create("collecttl").get("collecttl"),              // 收藏时间间隔
                                    storages.create("profilePicturetl").get("profilePicturetl"), // 头像点击时间间隔
                                    storages.create("imageTexttl").get("imageTexttl"),          // 图文时间间隔
                                    storages.create("backSpeed").get("backSpeed"),              // 返回速度
                                    storages.create("clickCommenttl").get("clickCommenttl"),    // 点击评论时间间隔
                                    carmine                                                      // 胭脂红参数
                                )
                                }
                              else {
                              log("12");
                                // 登录失败提示
                                toastLog(login_ret.message);
                                return;
                            }
                        } catch (e) {
                            return
                        }
                    }
                }
            })();
    }
    module.exports = convention;